@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    /* Vectal.ai Light Mode Colors */
    --background: 244 244 246;       /* #F3F4F6 - neutral gray background */
    --foreground: 31 41 55;          /* #1F2937 - dark gray text */
    --card: 255 255 255;             /* #FFFFFF - white cards */
    --card-foreground: 31 41 55;     /* #1F2937 - dark gray card text */
    --popover: 255 255 255;          /* #FFFFFF - white popover */
    --popover-foreground: 31 41 55;  /* #1F2937 - dark gray popover text */
    --primary: 59 130 246;           /* #3B82F6 - calming blue primary */
    --primary-foreground: 255 255 255; /* #FFFFFF - white text on primary */
    --secondary: 229 231 235;        /* #E5E7EB - light gray secondary */
    --secondary-foreground: 31 41 55; /* #1F2937 - dark gray secondary text */
    --muted: 249 250 251;            /* #F9FAFB - very light gray muted */
    --muted-foreground: 107 114 128; /* #6B7280 - medium gray muted text */
    --accent: 167 243 208;           /* #A7F3D0 - sage green accent */
    --accent-foreground: 31 41 55;   /* #1F2937 - dark gray accent text */
    --destructive: 239 68 68;        /* #EF4444 - red for destructive */
    --destructive-foreground: 255 255 255; /* #FFFFFF - white text on destructive */
    --border: 229 231 235;           /* #E5E7EB - light gray border */
    --input: 249 250 251;            /* #F9FAFB - very light gray input */
    --ring: 59 130 246;              /* #3B82F6 - blue focus ring */
    --radius: 0.5rem;
    --success: 34 197 94;            /* #22C55E - green for success */
    --warning: 245 158 11;           /* #F59E0B - amber for warning */
  }

  .dark {
    /* Vectal.ai Dark Mode Colors */
    --background: 31 41 55;          /* #1F2937 - dark gray background */
    --foreground: 243 244 246;       /* #F3F4F6 - light gray text */
    --card: 55 65 81;                /* #374151 - darker gray cards */
    --card-foreground: 243 244 246;  /* #F3F4F6 - light gray card text */
    --popover: 55 65 81;             /* #374151 - darker gray popover */
    --popover-foreground: 243 244 246; /* #F3F4F6 - light gray popover text */
    --primary: 96 165 250;           /* #60A5FA - lighter blue primary for dark mode */
    --primary-foreground: 31 41 55;  /* #1F2937 - dark gray text on primary */
    --secondary: 75 85 99;           /* #4B5563 - medium gray secondary */
    --secondary-foreground: 243 244 246; /* #F3F4F6 - light gray secondary text */
    --muted: 75 85 99;               /* #4B5563 - medium gray muted */
    --muted-foreground: 156 163 175; /* #9CA3AF - lighter gray muted text */
    --accent: 167 243 208;           /* #A7F3D0 - sage green accent (same in both modes) */
    --accent-foreground: 31 41 55;   /* #1F2937 - dark gray accent text */
    --destructive: 239 68 68;        /* #EF4444 - red for destructive */
    --destructive-foreground: 243 244 246; /* #F3F4F6 - light gray text on destructive */
    --border: 75 85 99;              /* #4B5563 - medium gray border */
    --input: 75 85 99;               /* #4B5563 - medium gray input */
    --ring: 96 165 250;              /* #60A5FA - lighter blue focus ring */
    --success: 34 197 94;            /* #22C55E - green for success */
    --warning: 245 158 11;           /* #F59E0B - amber for warning */
  }
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue', sans-serif;
  }
}

@layer components {
  /* Vectal.ai Design System Components */
  .vectal-card {
    @apply bg-card border border-border rounded-xl p-6 shadow-sm hover:shadow-md transition-all duration-300;
  }

  .vectal-button-primary {
    @apply bg-primary text-primary-foreground hover:bg-primary/90 active:bg-primary/95 px-4 py-2.5 rounded-lg font-medium transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed;
  }

  .vectal-button-secondary {
    @apply bg-secondary text-secondary-foreground hover:bg-secondary/80 active:bg-secondary/90 px-4 py-2.5 rounded-lg font-medium transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed;
  }

  .vectal-button-ghost {
    @apply text-foreground hover:bg-muted active:bg-muted/80 px-4 py-2.5 rounded-lg font-medium transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2;
  }

  .vectal-input {
    @apply bg-input border border-border rounded-lg px-3 py-2.5 text-foreground placeholder:text-muted-foreground focus:outline-none focus:ring-2 focus:ring-ring focus:border-primary transition-all duration-200;
  }

  .vectal-textarea {
    @apply bg-input border border-border rounded-lg px-3 py-2.5 text-foreground placeholder:text-muted-foreground focus:outline-none focus:ring-2 focus:ring-ring focus:border-primary transition-all duration-200 resize-none min-h-[100px];
  }

  .vectal-sidebar {
    @apply bg-card border-r border-border backdrop-blur-sm;
  }

  .vectal-task-card {
    @apply bg-card border border-border rounded-xl p-4 shadow-sm hover:shadow-md hover:border-primary/20 transition-all duration-300 cursor-pointer;
  }

  .vectal-modal-overlay {
    @apply fixed inset-0 bg-background/80 backdrop-blur-sm z-50 flex items-center justify-center p-4;
  }

  .vectal-modal {
    @apply bg-card border border-border rounded-xl shadow-xl max-w-md w-full max-h-[90vh] overflow-y-auto;
  }

  .vectal-badge {
    @apply inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium;
  }

  .vectal-badge-priority-high {
    @apply bg-destructive/10 text-destructive border border-destructive/20;
  }

  .vectal-badge-priority-medium {
    @apply bg-warning/10 text-warning border border-warning/20;
  }

  .vectal-badge-priority-low {
    @apply bg-success/10 text-success border border-success/20;
  }

  .vectal-badge-status {
    @apply bg-accent/10 text-accent-foreground border border-accent/20;
  }

  .vectal-divider {
    @apply border-t border-border my-4;
  }

  .vectal-scrollbar {
    scrollbar-width: thin;
    scrollbar-color: rgb(var(--muted)) transparent;
  }

  .vectal-scrollbar::-webkit-scrollbar {
    width: 6px;
  }

  .vectal-scrollbar::-webkit-scrollbar-track {
    background: transparent;
  }

  .vectal-scrollbar::-webkit-scrollbar-thumb {
    background-color: rgb(var(--muted));
    border-radius: 3px;
  }

  .vectal-scrollbar::-webkit-scrollbar-thumb:hover {
    background-color: rgb(var(--muted-foreground));
  }
}
