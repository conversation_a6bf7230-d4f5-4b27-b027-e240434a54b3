@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 222.2 84% 4.9%;
    --card: 0 0% 100%;
    --card-foreground: 222.2 84% 4.9%;
    --popover: 0 0% 100%;
    --popover-foreground: 222.2 84% 4.9%;
    --primary: 214 100% 59%;
    --primary-foreground: 210 40% 98%;
    --secondary: 210 40% 96%;
    --secondary-foreground: 222.2 84% 4.9%;
    --muted: 210 40% 96%;
    --muted-foreground: 215.4 16.3% 46.9%;
    --accent: 142 76% 73%;
    --accent-foreground: 222.2 84% 4.9%;
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;
    --border: 214.3 31.8% 91.4%;
    --input: 214.3 31.8% 91.4%;
    --ring: 214 100% 59%;
    --radius: 0.5rem;
  }

  .dark {
    /* Vectal.ai exact colors */
    --background: 220 13% 9%;        /* #171B1F - main background */
    --foreground: 210 40% 98%;       /* #FAFBFC - main text */
    --card: 220 13% 11%;             /* #1C2025 - card background */
    --card-foreground: 210 40% 98%;  /* #FAFBFC - card text */
    --popover: 220 13% 11%;          /* #1C2025 - popover background */
    --popover-foreground: 210 40% 98%; /* #FAFBFC - popover text */
    --primary: 45 93% 58%;           /* #F59E0B - orange/yellow accent */
    --primary-foreground: 220 13% 9%; /* #171B1F - text on primary */
    --secondary: 220 13% 15%;        /* #262B30 - secondary background */
    --secondary-foreground: 210 40% 98%; /* #FAFBFC - secondary text */
    --muted: 220 13% 15%;            /* #262B30 - muted background */
    --muted-foreground: 215 20.2% 65.1%; /* #9CA3AF - muted text */
    --accent: 45 93% 58%;            /* #F59E0B - accent color */
    --accent-foreground: 220 13% 9%; /* #171B1F - text on accent */
    --destructive: 0 84.2% 60.2%;    /* #EF4444 - red for destructive */
    --destructive-foreground: 210 40% 98%; /* #FAFBFC - text on destructive */
    --border: 220 13% 15%;           /* #262B30 - border color */
    --input: 220 13% 15%;            /* #262B30 - input background */
    --ring: 45 93% 58%;              /* #F59E0B - focus ring */
  }
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue', sans-serif;
  }
}

@layer components {
  /* Vectal.ai specific styles */
  .vectal-card {
    @apply bg-card border border-border rounded-lg p-4 shadow-sm;
  }

  .vectal-button-primary {
    @apply bg-primary text-primary-foreground hover:bg-primary/90 px-4 py-2 rounded-lg font-medium transition-all duration-200;
  }

  .vectal-button-secondary {
    @apply bg-secondary text-secondary-foreground hover:bg-secondary/80 px-4 py-2 rounded-lg font-medium transition-all duration-200;
  }

  .vectal-input {
    @apply bg-input border border-border rounded-lg px-3 py-2 text-foreground placeholder:text-muted-foreground focus:outline-none focus:ring-2 focus:ring-ring focus:border-transparent;
  }

  .vectal-sidebar {
    @apply bg-card border-r border-border;
  }

  .vectal-task-card {
    @apply bg-card border border-border rounded-lg p-3 shadow-sm hover:shadow-md transition-all duration-200 cursor-pointer;
  }
}
