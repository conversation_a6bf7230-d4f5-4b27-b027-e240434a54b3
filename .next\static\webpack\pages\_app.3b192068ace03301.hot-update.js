"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/_app",{

/***/ "./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[7].oneOf[13].use[1]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[7].oneOf[13].use[2]!./styles/globals.css":
/*!**********************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[7].oneOf[13].use[1]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[7].oneOf[13].use[2]!./styles/globals.css ***!
  \**********************************************************************************************************************************************************************************************************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var _node_modules_next_dist_build_webpack_loaders_css_loader_src_runtime_api_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../node_modules/next/dist/build/webpack/loaders/css-loader/src/runtime/api.js */ \"./node_modules/next/dist/build/webpack/loaders/css-loader/src/runtime/api.js\");\n/* harmony import */ var _node_modules_next_dist_build_webpack_loaders_css_loader_src_runtime_api_js__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_node_modules_next_dist_build_webpack_loaders_css_loader_src_runtime_api_js__WEBPACK_IMPORTED_MODULE_0__);\n// Imports\n\nvar ___CSS_LOADER_EXPORT___ = _node_modules_next_dist_build_webpack_loaders_css_loader_src_runtime_api_js__WEBPACK_IMPORTED_MODULE_0___default()(true);\n// Module\n___CSS_LOADER_EXPORT___.push([module.id, \"*, ::before, ::after {\\n  --tw-border-spacing-x: 0;\\n  --tw-border-spacing-y: 0;\\n  --tw-translate-x: 0;\\n  --tw-translate-y: 0;\\n  --tw-rotate: 0;\\n  --tw-skew-x: 0;\\n  --tw-skew-y: 0;\\n  --tw-scale-x: 1;\\n  --tw-scale-y: 1;\\n  --tw-pan-x:  ;\\n  --tw-pan-y:  ;\\n  --tw-pinch-zoom:  ;\\n  --tw-scroll-snap-strictness: proximity;\\n  --tw-gradient-from-position:  ;\\n  --tw-gradient-via-position:  ;\\n  --tw-gradient-to-position:  ;\\n  --tw-ordinal:  ;\\n  --tw-slashed-zero:  ;\\n  --tw-numeric-figure:  ;\\n  --tw-numeric-spacing:  ;\\n  --tw-numeric-fraction:  ;\\n  --tw-ring-inset:  ;\\n  --tw-ring-offset-width: 0px;\\n  --tw-ring-offset-color: #fff;\\n  --tw-ring-color: rgb(59 130 246 / 0.5);\\n  --tw-ring-offset-shadow: 0 0 #0000;\\n  --tw-ring-shadow: 0 0 #0000;\\n  --tw-shadow: 0 0 #0000;\\n  --tw-shadow-colored: 0 0 #0000;\\n  --tw-blur:  ;\\n  --tw-brightness:  ;\\n  --tw-contrast:  ;\\n  --tw-grayscale:  ;\\n  --tw-hue-rotate:  ;\\n  --tw-invert:  ;\\n  --tw-saturate:  ;\\n  --tw-sepia:  ;\\n  --tw-drop-shadow:  ;\\n  --tw-backdrop-blur:  ;\\n  --tw-backdrop-brightness:  ;\\n  --tw-backdrop-contrast:  ;\\n  --tw-backdrop-grayscale:  ;\\n  --tw-backdrop-hue-rotate:  ;\\n  --tw-backdrop-invert:  ;\\n  --tw-backdrop-opacity:  ;\\n  --tw-backdrop-saturate:  ;\\n  --tw-backdrop-sepia:  ;\\n  --tw-contain-size:  ;\\n  --tw-contain-layout:  ;\\n  --tw-contain-paint:  ;\\n  --tw-contain-style:  ;\\n}\\n\\n::backdrop {\\n  --tw-border-spacing-x: 0;\\n  --tw-border-spacing-y: 0;\\n  --tw-translate-x: 0;\\n  --tw-translate-y: 0;\\n  --tw-rotate: 0;\\n  --tw-skew-x: 0;\\n  --tw-skew-y: 0;\\n  --tw-scale-x: 1;\\n  --tw-scale-y: 1;\\n  --tw-pan-x:  ;\\n  --tw-pan-y:  ;\\n  --tw-pinch-zoom:  ;\\n  --tw-scroll-snap-strictness: proximity;\\n  --tw-gradient-from-position:  ;\\n  --tw-gradient-via-position:  ;\\n  --tw-gradient-to-position:  ;\\n  --tw-ordinal:  ;\\n  --tw-slashed-zero:  ;\\n  --tw-numeric-figure:  ;\\n  --tw-numeric-spacing:  ;\\n  --tw-numeric-fraction:  ;\\n  --tw-ring-inset:  ;\\n  --tw-ring-offset-width: 0px;\\n  --tw-ring-offset-color: #fff;\\n  --tw-ring-color: rgb(59 130 246 / 0.5);\\n  --tw-ring-offset-shadow: 0 0 #0000;\\n  --tw-ring-shadow: 0 0 #0000;\\n  --tw-shadow: 0 0 #0000;\\n  --tw-shadow-colored: 0 0 #0000;\\n  --tw-blur:  ;\\n  --tw-brightness:  ;\\n  --tw-contrast:  ;\\n  --tw-grayscale:  ;\\n  --tw-hue-rotate:  ;\\n  --tw-invert:  ;\\n  --tw-saturate:  ;\\n  --tw-sepia:  ;\\n  --tw-drop-shadow:  ;\\n  --tw-backdrop-blur:  ;\\n  --tw-backdrop-brightness:  ;\\n  --tw-backdrop-contrast:  ;\\n  --tw-backdrop-grayscale:  ;\\n  --tw-backdrop-hue-rotate:  ;\\n  --tw-backdrop-invert:  ;\\n  --tw-backdrop-opacity:  ;\\n  --tw-backdrop-saturate:  ;\\n  --tw-backdrop-sepia:  ;\\n  --tw-contain-size:  ;\\n  --tw-contain-layout:  ;\\n  --tw-contain-paint:  ;\\n  --tw-contain-style:  ;\\n}/*\\n! tailwindcss v3.4.17 | MIT License | https://tailwindcss.com\\n*//*\\n1. Prevent padding and border from affecting element width. (https://github.com/mozdevs/cssremedy/issues/4)\\n2. Allow adding a border to an element by just adding a border-width. (https://github.com/tailwindcss/tailwindcss/pull/116)\\n*/\\n\\n*,\\n::before,\\n::after {\\n  box-sizing: border-box; /* 1 */\\n  border-width: 0; /* 2 */\\n  border-style: solid; /* 2 */\\n  border-color: #e5e7eb; /* 2 */\\n}\\n\\n::before,\\n::after {\\n  --tw-content: '';\\n}\\n\\n/*\\n1. Use a consistent sensible line-height in all browsers.\\n2. Prevent adjustments of font size after orientation changes in iOS.\\n3. Use a more readable tab size.\\n4. Use the user's configured `sans` font-family by default.\\n5. Use the user's configured `sans` font-feature-settings by default.\\n6. Use the user's configured `sans` font-variation-settings by default.\\n7. Disable tap highlights on iOS\\n*/\\n\\nhtml,\\n:host {\\n  line-height: 1.5; /* 1 */\\n  -webkit-text-size-adjust: 100%; /* 2 */\\n  -moz-tab-size: 4; /* 3 */\\n  -o-tab-size: 4;\\n     tab-size: 4; /* 3 */\\n  font-family: ui-sans-serif, system-ui, sans-serif, \\\"Apple Color Emoji\\\", \\\"Segoe UI Emoji\\\", \\\"Segoe UI Symbol\\\", \\\"Noto Color Emoji\\\"; /* 4 */\\n  font-feature-settings: normal; /* 5 */\\n  font-variation-settings: normal; /* 6 */\\n  -webkit-tap-highlight-color: transparent; /* 7 */\\n}\\n\\n/*\\n1. Remove the margin in all browsers.\\n2. Inherit line-height from `html` so users can set them as a class directly on the `html` element.\\n*/\\n\\nbody {\\n  margin: 0; /* 1 */\\n  line-height: inherit; /* 2 */\\n}\\n\\n/*\\n1. Add the correct height in Firefox.\\n2. Correct the inheritance of border color in Firefox. (https://bugzilla.mozilla.org/show_bug.cgi?id=190655)\\n3. Ensure horizontal rules are visible by default.\\n*/\\n\\nhr {\\n  height: 0; /* 1 */\\n  color: inherit; /* 2 */\\n  border-top-width: 1px; /* 3 */\\n}\\n\\n/*\\nAdd the correct text decoration in Chrome, Edge, and Safari.\\n*/\\n\\nabbr:where([title]) {\\n  -webkit-text-decoration: underline dotted;\\n          text-decoration: underline dotted;\\n}\\n\\n/*\\nRemove the default font size and weight for headings.\\n*/\\n\\nh1,\\nh2,\\nh3,\\nh4,\\nh5,\\nh6 {\\n  font-size: inherit;\\n  font-weight: inherit;\\n}\\n\\n/*\\nReset links to optimize for opt-in styling instead of opt-out.\\n*/\\n\\na {\\n  color: inherit;\\n  text-decoration: inherit;\\n}\\n\\n/*\\nAdd the correct font weight in Edge and Safari.\\n*/\\n\\nb,\\nstrong {\\n  font-weight: bolder;\\n}\\n\\n/*\\n1. Use the user's configured `mono` font-family by default.\\n2. Use the user's configured `mono` font-feature-settings by default.\\n3. Use the user's configured `mono` font-variation-settings by default.\\n4. Correct the odd `em` font sizing in all browsers.\\n*/\\n\\ncode,\\nkbd,\\nsamp,\\npre {\\n  font-family: ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, \\\"Liberation Mono\\\", \\\"Courier New\\\", monospace; /* 1 */\\n  font-feature-settings: normal; /* 2 */\\n  font-variation-settings: normal; /* 3 */\\n  font-size: 1em; /* 4 */\\n}\\n\\n/*\\nAdd the correct font size in all browsers.\\n*/\\n\\nsmall {\\n  font-size: 80%;\\n}\\n\\n/*\\nPrevent `sub` and `sup` elements from affecting the line height in all browsers.\\n*/\\n\\nsub,\\nsup {\\n  font-size: 75%;\\n  line-height: 0;\\n  position: relative;\\n  vertical-align: baseline;\\n}\\n\\nsub {\\n  bottom: -0.25em;\\n}\\n\\nsup {\\n  top: -0.5em;\\n}\\n\\n/*\\n1. Remove text indentation from table contents in Chrome and Safari. (https://bugs.chromium.org/p/chromium/issues/detail?id=999088, https://bugs.webkit.org/show_bug.cgi?id=201297)\\n2. Correct table border color inheritance in all Chrome and Safari. (https://bugs.chromium.org/p/chromium/issues/detail?id=935729, https://bugs.webkit.org/show_bug.cgi?id=195016)\\n3. Remove gaps between table borders by default.\\n*/\\n\\ntable {\\n  text-indent: 0; /* 1 */\\n  border-color: inherit; /* 2 */\\n  border-collapse: collapse; /* 3 */\\n}\\n\\n/*\\n1. Change the font styles in all browsers.\\n2. Remove the margin in Firefox and Safari.\\n3. Remove default padding in all browsers.\\n*/\\n\\nbutton,\\ninput,\\noptgroup,\\nselect,\\ntextarea {\\n  font-family: inherit; /* 1 */\\n  font-feature-settings: inherit; /* 1 */\\n  font-variation-settings: inherit; /* 1 */\\n  font-size: 100%; /* 1 */\\n  font-weight: inherit; /* 1 */\\n  line-height: inherit; /* 1 */\\n  letter-spacing: inherit; /* 1 */\\n  color: inherit; /* 1 */\\n  margin: 0; /* 2 */\\n  padding: 0; /* 3 */\\n}\\n\\n/*\\nRemove the inheritance of text transform in Edge and Firefox.\\n*/\\n\\nbutton,\\nselect {\\n  text-transform: none;\\n}\\n\\n/*\\n1. Correct the inability to style clickable types in iOS and Safari.\\n2. Remove default button styles.\\n*/\\n\\nbutton,\\ninput:where([type='button']),\\ninput:where([type='reset']),\\ninput:where([type='submit']) {\\n  -webkit-appearance: button; /* 1 */\\n  background-color: transparent; /* 2 */\\n  background-image: none; /* 2 */\\n}\\n\\n/*\\nUse the modern Firefox focus style for all focusable elements.\\n*/\\n\\n:-moz-focusring {\\n  outline: auto;\\n}\\n\\n/*\\nRemove the additional `:invalid` styles in Firefox. (https://github.com/mozilla/gecko-dev/blob/2f9eacd9d3d995c937b4251a5557d95d494c9be1/layout/style/res/forms.css#L728-L737)\\n*/\\n\\n:-moz-ui-invalid {\\n  box-shadow: none;\\n}\\n\\n/*\\nAdd the correct vertical alignment in Chrome and Firefox.\\n*/\\n\\nprogress {\\n  vertical-align: baseline;\\n}\\n\\n/*\\nCorrect the cursor style of increment and decrement buttons in Safari.\\n*/\\n\\n::-webkit-inner-spin-button,\\n::-webkit-outer-spin-button {\\n  height: auto;\\n}\\n\\n/*\\n1. Correct the odd appearance in Chrome and Safari.\\n2. Correct the outline style in Safari.\\n*/\\n\\n[type='search'] {\\n  -webkit-appearance: textfield; /* 1 */\\n  outline-offset: -2px; /* 2 */\\n}\\n\\n/*\\nRemove the inner padding in Chrome and Safari on macOS.\\n*/\\n\\n::-webkit-search-decoration {\\n  -webkit-appearance: none;\\n}\\n\\n/*\\n1. Correct the inability to style clickable types in iOS and Safari.\\n2. Change font properties to `inherit` in Safari.\\n*/\\n\\n::-webkit-file-upload-button {\\n  -webkit-appearance: button; /* 1 */\\n  font: inherit; /* 2 */\\n}\\n\\n/*\\nAdd the correct display in Chrome and Safari.\\n*/\\n\\nsummary {\\n  display: list-item;\\n}\\n\\n/*\\nRemoves the default spacing and border for appropriate elements.\\n*/\\n\\nblockquote,\\ndl,\\ndd,\\nh1,\\nh2,\\nh3,\\nh4,\\nh5,\\nh6,\\nhr,\\nfigure,\\np,\\npre {\\n  margin: 0;\\n}\\n\\nfieldset {\\n  margin: 0;\\n  padding: 0;\\n}\\n\\nlegend {\\n  padding: 0;\\n}\\n\\nol,\\nul,\\nmenu {\\n  list-style: none;\\n  margin: 0;\\n  padding: 0;\\n}\\n\\n/*\\nReset default styling for dialogs.\\n*/\\ndialog {\\n  padding: 0;\\n}\\n\\n/*\\nPrevent resizing textareas horizontally by default.\\n*/\\n\\ntextarea {\\n  resize: vertical;\\n}\\n\\n/*\\n1. Reset the default placeholder opacity in Firefox. (https://github.com/tailwindlabs/tailwindcss/issues/3300)\\n2. Set the default placeholder color to the user's configured gray 400 color.\\n*/\\n\\ninput::-moz-placeholder, textarea::-moz-placeholder {\\n  opacity: 1; /* 1 */\\n  color: #9ca3af; /* 2 */\\n}\\n\\ninput::placeholder,\\ntextarea::placeholder {\\n  opacity: 1; /* 1 */\\n  color: #9ca3af; /* 2 */\\n}\\n\\n/*\\nSet the default cursor for buttons.\\n*/\\n\\nbutton,\\n[role=\\\"button\\\"] {\\n  cursor: pointer;\\n}\\n\\n/*\\nMake sure disabled buttons don't get the pointer cursor.\\n*/\\n:disabled {\\n  cursor: default;\\n}\\n\\n/*\\n1. Make replaced elements `display: block` by default. (https://github.com/mozdevs/cssremedy/issues/14)\\n2. Add `vertical-align: middle` to align replaced elements more sensibly by default. (https://github.com/jensimmons/cssremedy/issues/14#issuecomment-634934210)\\n   This can trigger a poorly considered lint error in some tools but is included by design.\\n*/\\n\\nimg,\\nsvg,\\nvideo,\\ncanvas,\\naudio,\\niframe,\\nembed,\\nobject {\\n  display: block; /* 1 */\\n  vertical-align: middle; /* 2 */\\n}\\n\\n/*\\nConstrain images and videos to the parent width and preserve their intrinsic aspect ratio. (https://github.com/mozdevs/cssremedy/issues/14)\\n*/\\n\\nimg,\\nvideo {\\n  max-width: 100%;\\n  height: auto;\\n}\\n\\n/* Make elements with the HTML hidden attribute stay hidden by default */\\n[hidden]:where(:not([hidden=\\\"until-found\\\"])) {\\n  display: none;\\n}\\n  :root {\\n    /* Vectal.ai Light Mode Colors */\\n    --background: 244 244 246;       /* #F3F4F6 - neutral gray background */\\n    --foreground: 31 41 55;          /* #1F2937 - dark gray text */\\n    --card: 255 255 255;             /* #FFFFFF - white cards */\\n    --card-foreground: 31 41 55;     /* #1F2937 - dark gray card text */\\n    --popover: 255 255 255;          /* #FFFFFF - white popover */\\n    --popover-foreground: 31 41 55;  /* #1F2937 - dark gray popover text */\\n    --primary: 59 130 246;           /* #3B82F6 - calming blue primary */\\n    --primary-foreground: 255 255 255; /* #FFFFFF - white text on primary */\\n    --secondary: 229 231 235;        /* #E5E7EB - light gray secondary */\\n    --secondary-foreground: 31 41 55; /* #1F2937 - dark gray secondary text */\\n    --muted: 249 250 251;            /* #F9FAFB - very light gray muted */\\n    --muted-foreground: 107 114 128; /* #6B7280 - medium gray muted text */\\n    --accent: 167 243 208;           /* #A7F3D0 - sage green accent */\\n    --accent-foreground: 31 41 55;   /* #1F2937 - dark gray accent text */\\n    --destructive: 239 68 68;        /* #EF4444 - red for destructive */\\n    --destructive-foreground: 255 255 255; /* #FFFFFF - white text on destructive */\\n    --border: 229 231 235;           /* #E5E7EB - light gray border */\\n    --input: 249 250 251;            /* #F9FAFB - very light gray input */\\n    --ring: 59 130 246;              /* #3B82F6 - blue focus ring */\\n    --radius: 0.5rem;\\n    --success: 34 197 94;            /* #22C55E - green for success */\\n    --warning: 245 158 11;           /* #F59E0B - amber for warning */\\n  }\\n  * {\\n  border-color: rgb(var(--border));\\n}\\n  body {\\n  background-color: rgb(var(--background));\\n  color: rgb(var(--foreground));\\n    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue', sans-serif;\\n}\\n/* Vectal.ai Design System Components */\\n.vectal-button-primary {\\n  border-radius: var(--radius);\\n  background-color: rgb(var(--primary));\\n  padding-left: 1rem;\\n  padding-right: 1rem;\\n  padding-top: 0.625rem;\\n  padding-bottom: 0.625rem;\\n  font-weight: 500;\\n  color: rgb(var(--primary-foreground));\\n  transition-property: all;\\n  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);\\n  transition-duration: 200ms;\\n}\\n.vectal-button-primary:hover {\\n  background-color: rgb(var(--primary) / 0.9);\\n}\\n.vectal-button-primary:focus {\\n  outline: 2px solid transparent;\\n  outline-offset: 2px;\\n  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);\\n  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color);\\n  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);\\n  --tw-ring-color: rgb(var(--ring));\\n  --tw-ring-offset-width: 2px;\\n}\\n.vectal-button-primary:active {\\n  background-color: rgb(var(--primary) / 0.95);\\n}\\n.vectal-button-primary:disabled {\\n  cursor: not-allowed;\\n  opacity: 0.5;\\n}\\n.vectal-input {\\n  border-radius: var(--radius);\\n  border-width: 1px;\\n  border-color: rgb(var(--border));\\n  background-color: rgb(var(--input));\\n  padding-left: 0.75rem;\\n  padding-right: 0.75rem;\\n  padding-top: 0.625rem;\\n  padding-bottom: 0.625rem;\\n  color: rgb(var(--foreground));\\n  transition-property: all;\\n  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);\\n  transition-duration: 200ms;\\n}\\n.vectal-input::-moz-placeholder {\\n  color: rgb(var(--muted-foreground));\\n}\\n.vectal-input::placeholder {\\n  color: rgb(var(--muted-foreground));\\n}\\n.vectal-input:focus {\\n  border-color: rgb(var(--primary));\\n  outline: 2px solid transparent;\\n  outline-offset: 2px;\\n  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);\\n  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color);\\n  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);\\n  --tw-ring-color: rgb(var(--ring));\\n}\\n.vectal-sidebar {\\n  border-right-width: 1px;\\n  border-color: rgb(var(--border));\\n  background-color: rgb(var(--card));\\n  --tw-backdrop-blur: blur(4px);\\n  -webkit-backdrop-filter: var(--tw-backdrop-blur) var(--tw-backdrop-brightness) var(--tw-backdrop-contrast) var(--tw-backdrop-grayscale) var(--tw-backdrop-hue-rotate) var(--tw-backdrop-invert) var(--tw-backdrop-opacity) var(--tw-backdrop-saturate) var(--tw-backdrop-sepia);\\n  backdrop-filter: var(--tw-backdrop-blur) var(--tw-backdrop-brightness) var(--tw-backdrop-contrast) var(--tw-backdrop-grayscale) var(--tw-backdrop-hue-rotate) var(--tw-backdrop-invert) var(--tw-backdrop-opacity) var(--tw-backdrop-saturate) var(--tw-backdrop-sepia);\\n}\\n.vectal-task-card {\\n  cursor: pointer;\\n  border-radius: 0.75rem;\\n  border-width: 1px;\\n  border-color: rgb(var(--border));\\n  background-color: rgb(var(--card));\\n  padding: 1rem;\\n  --tw-shadow: 0 1px 2px 0 rgb(0 0 0 / 0.05);\\n  --tw-shadow-colored: 0 1px 2px 0 var(--tw-shadow-color);\\n  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);\\n  transition-property: all;\\n  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);\\n  transition-duration: 300ms;\\n}\\n.vectal-task-card:hover {\\n  border-color: rgb(var(--primary) / 0.2);\\n  --tw-shadow: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);\\n  --tw-shadow-colored: 0 4px 6px -1px var(--tw-shadow-color), 0 2px 4px -2px var(--tw-shadow-color);\\n  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);\\n}\\n.vectal-scrollbar {\\n    scrollbar-width: thin;\\n    scrollbar-color: rgb(var(--muted)) transparent;\\n  }\\n.vectal-scrollbar::-webkit-scrollbar {\\n    width: 6px;\\n  }\\n.vectal-scrollbar::-webkit-scrollbar-track {\\n    background: transparent;\\n  }\\n.vectal-scrollbar::-webkit-scrollbar-thumb {\\n    background-color: rgb(var(--muted));\\n    border-radius: 3px;\\n  }\\n.vectal-scrollbar::-webkit-scrollbar-thumb:hover {\\n    background-color: rgb(var(--muted-foreground));\\n  }\\n.sr-only {\\n  position: absolute;\\n  width: 1px;\\n  height: 1px;\\n  padding: 0;\\n  margin: -1px;\\n  overflow: hidden;\\n  clip: rect(0, 0, 0, 0);\\n  white-space: nowrap;\\n  border-width: 0;\\n}\\n.pointer-events-none {\\n  pointer-events: none;\\n}\\n.fixed {\\n  position: fixed;\\n}\\n.absolute {\\n  position: absolute;\\n}\\n.relative {\\n  position: relative;\\n}\\n.inset-0 {\\n  inset: 0px;\\n}\\n.inset-y-0 {\\n  top: 0px;\\n  bottom: 0px;\\n}\\n.bottom-6 {\\n  bottom: 1.5rem;\\n}\\n.left-2 {\\n  left: 0.5rem;\\n}\\n.left-3 {\\n  left: 0.75rem;\\n}\\n.left-4 {\\n  left: 1rem;\\n}\\n.left-\\\\[50\\\\%\\\\] {\\n  left: 50%;\\n}\\n.right-0 {\\n  right: 0px;\\n}\\n.right-2 {\\n  right: 0.5rem;\\n}\\n.right-4 {\\n  right: 1rem;\\n}\\n.right-6 {\\n  right: 1.5rem;\\n}\\n.top-1\\\\/2 {\\n  top: 50%;\\n}\\n.top-2 {\\n  top: 0.5rem;\\n}\\n.top-20 {\\n  top: 5rem;\\n}\\n.top-4 {\\n  top: 1rem;\\n}\\n.top-7 {\\n  top: 1.75rem;\\n}\\n.top-8 {\\n  top: 2rem;\\n}\\n.top-\\\\[50\\\\%\\\\] {\\n  top: 50%;\\n}\\n.z-10 {\\n  z-index: 10;\\n}\\n.z-50 {\\n  z-index: 50;\\n}\\n.-mx-1 {\\n  margin-left: -0.25rem;\\n  margin-right: -0.25rem;\\n}\\n.mx-4 {\\n  margin-left: 1rem;\\n  margin-right: 1rem;\\n}\\n.mx-auto {\\n  margin-left: auto;\\n  margin-right: auto;\\n}\\n.my-1 {\\n  margin-top: 0.25rem;\\n  margin-bottom: 0.25rem;\\n}\\n.mb-1 {\\n  margin-bottom: 0.25rem;\\n}\\n.mb-2 {\\n  margin-bottom: 0.5rem;\\n}\\n.mb-3 {\\n  margin-bottom: 0.75rem;\\n}\\n.mb-4 {\\n  margin-bottom: 1rem;\\n}\\n.mb-6 {\\n  margin-bottom: 1.5rem;\\n}\\n.ml-1 {\\n  margin-left: 0.25rem;\\n}\\n.ml-4 {\\n  margin-left: 1rem;\\n}\\n.mr-1 {\\n  margin-right: 0.25rem;\\n}\\n.mr-2 {\\n  margin-right: 0.5rem;\\n}\\n.mt-0\\\\.5 {\\n  margin-top: 0.125rem;\\n}\\n.mt-1 {\\n  margin-top: 0.25rem;\\n}\\n.mt-2 {\\n  margin-top: 0.5rem;\\n}\\n.mt-3 {\\n  margin-top: 0.75rem;\\n}\\n.mt-8 {\\n  margin-top: 2rem;\\n}\\n.line-clamp-2 {\\n  overflow: hidden;\\n  display: -webkit-box;\\n  -webkit-box-orient: vertical;\\n  -webkit-line-clamp: 2;\\n}\\n.block {\\n  display: block;\\n}\\n.flex {\\n  display: flex;\\n}\\n.inline-flex {\\n  display: inline-flex;\\n}\\n.grid {\\n  display: grid;\\n}\\n.h-10 {\\n  height: 2.5rem;\\n}\\n.h-11 {\\n  height: 2.75rem;\\n}\\n.h-12 {\\n  height: 3rem;\\n}\\n.h-16 {\\n  height: 4rem;\\n}\\n.h-2 {\\n  height: 0.5rem;\\n}\\n.h-2\\\\.5 {\\n  height: 0.625rem;\\n}\\n.h-3 {\\n  height: 0.75rem;\\n}\\n.h-3\\\\.5 {\\n  height: 0.875rem;\\n}\\n.h-32 {\\n  height: 8rem;\\n}\\n.h-4 {\\n  height: 1rem;\\n}\\n.h-5 {\\n  height: 1.25rem;\\n}\\n.h-6 {\\n  height: 1.5rem;\\n}\\n.h-8 {\\n  height: 2rem;\\n}\\n.h-80 {\\n  height: 20rem;\\n}\\n.h-9 {\\n  height: 2.25rem;\\n}\\n.h-\\\\[32rem\\\\] {\\n  height: 32rem;\\n}\\n.h-\\\\[500px\\\\] {\\n  height: 500px;\\n}\\n.h-\\\\[var\\\\(--radix-select-trigger-height\\\\)\\\\] {\\n  height: var(--radix-select-trigger-height);\\n}\\n.h-full {\\n  height: 100%;\\n}\\n.h-px {\\n  height: 1px;\\n}\\n.h-screen {\\n  height: 100vh;\\n}\\n.max-h-96 {\\n  max-height: 24rem;\\n}\\n.max-h-\\\\[90vh\\\\] {\\n  max-height: 90vh;\\n}\\n.min-h-\\\\[100px\\\\] {\\n  min-height: 100px;\\n}\\n.min-h-\\\\[200px\\\\] {\\n  min-height: 200px;\\n}\\n.min-h-\\\\[80px\\\\] {\\n  min-height: 80px;\\n}\\n.min-h-screen {\\n  min-height: 100vh;\\n}\\n.w-10 {\\n  width: 2.5rem;\\n}\\n.w-12 {\\n  width: 3rem;\\n}\\n.w-16 {\\n  width: 4rem;\\n}\\n.w-2 {\\n  width: 0.5rem;\\n}\\n.w-2\\\\.5 {\\n  width: 0.625rem;\\n}\\n.w-3 {\\n  width: 0.75rem;\\n}\\n.w-3\\\\.5 {\\n  width: 0.875rem;\\n}\\n.w-4 {\\n  width: 1rem;\\n}\\n.w-5 {\\n  width: 1.25rem;\\n}\\n.w-6 {\\n  width: 1.5rem;\\n}\\n.w-64 {\\n  width: 16rem;\\n}\\n.w-8 {\\n  width: 2rem;\\n}\\n.w-80 {\\n  width: 20rem;\\n}\\n.w-96 {\\n  width: 24rem;\\n}\\n.w-full {\\n  width: 100%;\\n}\\n.min-w-0 {\\n  min-width: 0px;\\n}\\n.min-w-\\\\[120px\\\\] {\\n  min-width: 120px;\\n}\\n.min-w-\\\\[8rem\\\\] {\\n  min-width: 8rem;\\n}\\n.min-w-\\\\[var\\\\(--radix-select-trigger-width\\\\)\\\\] {\\n  min-width: var(--radix-select-trigger-width);\\n}\\n.max-w-2xl {\\n  max-width: 42rem;\\n}\\n.max-w-\\\\[80\\\\%\\\\] {\\n  max-width: 80%;\\n}\\n.max-w-\\\\[85\\\\%\\\\] {\\n  max-width: 85%;\\n}\\n.max-w-lg {\\n  max-width: 32rem;\\n}\\n.max-w-md {\\n  max-width: 28rem;\\n}\\n.flex-1 {\\n  flex: 1 1 0%;\\n}\\n.flex-shrink-0 {\\n  flex-shrink: 0;\\n}\\n.-translate-y-1\\\\/2 {\\n  --tw-translate-y: -50%;\\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\\n}\\n.translate-x-\\\\[-50\\\\%\\\\] {\\n  --tw-translate-x: -50%;\\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\\n}\\n.translate-y-\\\\[-50\\\\%\\\\] {\\n  --tw-translate-y: -50%;\\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\\n}\\n.-rotate-90 {\\n  --tw-rotate: -90deg;\\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\\n}\\n.rotate-0 {\\n  --tw-rotate: 0deg;\\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\\n}\\n.rotate-3 {\\n  --tw-rotate: 3deg;\\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\\n}\\n.rotate-90 {\\n  --tw-rotate: 90deg;\\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\\n}\\n.scale-0 {\\n  --tw-scale-x: 0;\\n  --tw-scale-y: 0;\\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\\n}\\n.scale-100 {\\n  --tw-scale-x: 1;\\n  --tw-scale-y: 1;\\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\\n}\\n.scale-105 {\\n  --tw-scale-x: 1.05;\\n  --tw-scale-y: 1.05;\\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\\n}\\n.transform {\\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\\n}\\n@keyframes bounce {\\n\\n  0%, 100% {\\n    transform: translateY(-25%);\\n    animation-timing-function: cubic-bezier(0.8,0,1,1);\\n  }\\n\\n  50% {\\n    transform: none;\\n    animation-timing-function: cubic-bezier(0,0,0.2,1);\\n  }\\n}\\n.animate-bounce {\\n  animation: bounce 1s infinite;\\n}\\n@keyframes pulse {\\n\\n  50% {\\n    opacity: .5;\\n  }\\n}\\n.animate-pulse {\\n  animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;\\n}\\n@keyframes spin {\\n\\n  to {\\n    transform: rotate(360deg);\\n  }\\n}\\n.animate-spin {\\n  animation: spin 1s linear infinite;\\n}\\n.cursor-default {\\n  cursor: default;\\n}\\n.cursor-grab {\\n  cursor: grab;\\n}\\n.cursor-pointer {\\n  cursor: pointer;\\n}\\n.select-none {\\n  -webkit-user-select: none;\\n     -moz-user-select: none;\\n          user-select: none;\\n}\\n.resize-none {\\n  resize: none;\\n}\\n.list-decimal {\\n  list-style-type: decimal;\\n}\\n.grid-cols-1 {\\n  grid-template-columns: repeat(1, minmax(0, 1fr));\\n}\\n.grid-cols-2 {\\n  grid-template-columns: repeat(2, minmax(0, 1fr));\\n}\\n.grid-cols-3 {\\n  grid-template-columns: repeat(3, minmax(0, 1fr));\\n}\\n.flex-row-reverse {\\n  flex-direction: row-reverse;\\n}\\n.flex-col {\\n  flex-direction: column;\\n}\\n.flex-col-reverse {\\n  flex-direction: column-reverse;\\n}\\n.flex-wrap {\\n  flex-wrap: wrap;\\n}\\n.items-start {\\n  align-items: flex-start;\\n}\\n.items-center {\\n  align-items: center;\\n}\\n.justify-start {\\n  justify-content: flex-start;\\n}\\n.justify-end {\\n  justify-content: flex-end;\\n}\\n.justify-center {\\n  justify-content: center;\\n}\\n.justify-between {\\n  justify-content: space-between;\\n}\\n.gap-1 {\\n  gap: 0.25rem;\\n}\\n.gap-2 {\\n  gap: 0.5rem;\\n}\\n.gap-3 {\\n  gap: 0.75rem;\\n}\\n.gap-4 {\\n  gap: 1rem;\\n}\\n.gap-6 {\\n  gap: 1.5rem;\\n}\\n.space-x-1 > :not([hidden]) ~ :not([hidden]) {\\n  --tw-space-x-reverse: 0;\\n  margin-right: calc(0.25rem * var(--tw-space-x-reverse));\\n  margin-left: calc(0.25rem * calc(1 - var(--tw-space-x-reverse)));\\n}\\n.space-x-2 > :not([hidden]) ~ :not([hidden]) {\\n  --tw-space-x-reverse: 0;\\n  margin-right: calc(0.5rem * var(--tw-space-x-reverse));\\n  margin-left: calc(0.5rem * calc(1 - var(--tw-space-x-reverse)));\\n}\\n.space-x-3 > :not([hidden]) ~ :not([hidden]) {\\n  --tw-space-x-reverse: 0;\\n  margin-right: calc(0.75rem * var(--tw-space-x-reverse));\\n  margin-left: calc(0.75rem * calc(1 - var(--tw-space-x-reverse)));\\n}\\n.space-x-4 > :not([hidden]) ~ :not([hidden]) {\\n  --tw-space-x-reverse: 0;\\n  margin-right: calc(1rem * var(--tw-space-x-reverse));\\n  margin-left: calc(1rem * calc(1 - var(--tw-space-x-reverse)));\\n}\\n.space-y-1 > :not([hidden]) ~ :not([hidden]) {\\n  --tw-space-y-reverse: 0;\\n  margin-top: calc(0.25rem * calc(1 - var(--tw-space-y-reverse)));\\n  margin-bottom: calc(0.25rem * var(--tw-space-y-reverse));\\n}\\n.space-y-1\\\\.5 > :not([hidden]) ~ :not([hidden]) {\\n  --tw-space-y-reverse: 0;\\n  margin-top: calc(0.375rem * calc(1 - var(--tw-space-y-reverse)));\\n  margin-bottom: calc(0.375rem * var(--tw-space-y-reverse));\\n}\\n.space-y-2 > :not([hidden]) ~ :not([hidden]) {\\n  --tw-space-y-reverse: 0;\\n  margin-top: calc(0.5rem * calc(1 - var(--tw-space-y-reverse)));\\n  margin-bottom: calc(0.5rem * var(--tw-space-y-reverse));\\n}\\n.space-y-3 > :not([hidden]) ~ :not([hidden]) {\\n  --tw-space-y-reverse: 0;\\n  margin-top: calc(0.75rem * calc(1 - var(--tw-space-y-reverse)));\\n  margin-bottom: calc(0.75rem * var(--tw-space-y-reverse));\\n}\\n.space-y-4 > :not([hidden]) ~ :not([hidden]) {\\n  --tw-space-y-reverse: 0;\\n  margin-top: calc(1rem * calc(1 - var(--tw-space-y-reverse)));\\n  margin-bottom: calc(1rem * var(--tw-space-y-reverse));\\n}\\n.space-y-5 > :not([hidden]) ~ :not([hidden]) {\\n  --tw-space-y-reverse: 0;\\n  margin-top: calc(1.25rem * calc(1 - var(--tw-space-y-reverse)));\\n  margin-bottom: calc(1.25rem * var(--tw-space-y-reverse));\\n}\\n.space-y-6 > :not([hidden]) ~ :not([hidden]) {\\n  --tw-space-y-reverse: 0;\\n  margin-top: calc(1.5rem * calc(1 - var(--tw-space-y-reverse)));\\n  margin-bottom: calc(1.5rem * var(--tw-space-y-reverse));\\n}\\n.space-y-8 > :not([hidden]) ~ :not([hidden]) {\\n  --tw-space-y-reverse: 0;\\n  margin-top: calc(2rem * calc(1 - var(--tw-space-y-reverse)));\\n  margin-bottom: calc(2rem * var(--tw-space-y-reverse));\\n}\\n.space-x-reverse > :not([hidden]) ~ :not([hidden]) {\\n  --tw-space-x-reverse: 1;\\n}\\n.overflow-hidden {\\n  overflow: hidden;\\n}\\n.overflow-x-auto {\\n  overflow-x: auto;\\n}\\n.overflow-y-auto {\\n  overflow-y: auto;\\n}\\n.whitespace-nowrap {\\n  white-space: nowrap;\\n}\\n.whitespace-pre-wrap {\\n  white-space: pre-wrap;\\n}\\n.rounded {\\n  border-radius: 0.25rem;\\n}\\n.rounded-2xl {\\n  border-radius: 1rem;\\n}\\n.rounded-full {\\n  border-radius: 9999px;\\n}\\n.rounded-lg {\\n  border-radius: var(--radius);\\n}\\n.rounded-md {\\n  border-radius: calc(var(--radius) - 2px);\\n}\\n.rounded-sm {\\n  border-radius: calc(var(--radius) - 4px);\\n}\\n.rounded-xl {\\n  border-radius: 0.75rem;\\n}\\n.rounded-t-xl {\\n  border-top-left-radius: 0.75rem;\\n  border-top-right-radius: 0.75rem;\\n}\\n.rounded-bl-md {\\n  border-bottom-left-radius: calc(var(--radius) - 2px);\\n}\\n.rounded-br-md {\\n  border-bottom-right-radius: calc(var(--radius) - 2px);\\n}\\n.border {\\n  border-width: 1px;\\n}\\n.border-2 {\\n  border-width: 2px;\\n}\\n.border-b {\\n  border-bottom-width: 1px;\\n}\\n.border-b-2 {\\n  border-bottom-width: 2px;\\n}\\n.border-l-4 {\\n  border-left-width: 4px;\\n}\\n.border-r {\\n  border-right-width: 1px;\\n}\\n.border-t {\\n  border-top-width: 1px;\\n}\\n.border-dashed {\\n  border-style: dashed;\\n}\\n.border-none {\\n  border-style: none;\\n}\\n.border-accent {\\n  border-color: rgb(var(--accent));\\n}\\n.border-accent\\\\/20 {\\n  border-color: rgb(var(--accent) / 0.2);\\n}\\n.border-accent\\\\/30 {\\n  border-color: rgb(var(--accent) / 0.3);\\n}\\n.border-blue-200 {\\n  --tw-border-opacity: 1;\\n  border-color: rgb(191 219 254 / var(--tw-border-opacity, 1));\\n}\\n.border-border {\\n  border-color: rgb(var(--border));\\n}\\n.border-destructive {\\n  border-color: rgb(var(--destructive));\\n}\\n.border-destructive\\\\/20 {\\n  border-color: rgb(var(--destructive) / 0.2);\\n}\\n.border-gray-200 {\\n  --tw-border-opacity: 1;\\n  border-color: rgb(229 231 235 / var(--tw-border-opacity, 1));\\n}\\n.border-gray-300 {\\n  --tw-border-opacity: 1;\\n  border-color: rgb(209 213 219 / var(--tw-border-opacity, 1));\\n}\\n.border-green-200 {\\n  --tw-border-opacity: 1;\\n  border-color: rgb(187 247 208 / var(--tw-border-opacity, 1));\\n}\\n.border-muted {\\n  border-color: rgb(var(--muted));\\n}\\n.border-primary {\\n  border-color: rgb(var(--primary));\\n}\\n.border-primary\\\\/20 {\\n  border-color: rgb(var(--primary) / 0.2);\\n}\\n.border-primary\\\\/30 {\\n  border-color: rgb(var(--primary) / 0.3);\\n}\\n.border-red-200 {\\n  --tw-border-opacity: 1;\\n  border-color: rgb(254 202 202 / var(--tw-border-opacity, 1));\\n}\\n.border-success\\\\/20 {\\n  border-color: rgb(var(--success) / 0.2);\\n}\\n.border-transparent {\\n  border-color: transparent;\\n}\\n.border-warning\\\\/20 {\\n  border-color: rgb(var(--warning) / 0.2);\\n}\\n.border-white {\\n  --tw-border-opacity: 1;\\n  border-color: rgb(255 255 255 / var(--tw-border-opacity, 1));\\n}\\n.border-yellow-200 {\\n  --tw-border-opacity: 1;\\n  border-color: rgb(254 240 138 / var(--tw-border-opacity, 1));\\n}\\n.border-l-gray-400 {\\n  --tw-border-opacity: 1;\\n  border-left-color: rgb(156 163 175 / var(--tw-border-opacity, 1));\\n}\\n.border-l-green-500 {\\n  --tw-border-opacity: 1;\\n  border-left-color: rgb(34 197 94 / var(--tw-border-opacity, 1));\\n}\\n.border-l-primary {\\n  border-left-color: rgb(var(--primary));\\n}\\n.bg-accent {\\n  background-color: rgb(var(--accent));\\n}\\n.bg-accent\\\\/10 {\\n  background-color: rgb(var(--accent) / 0.1);\\n}\\n.bg-background {\\n  background-color: rgb(var(--background));\\n}\\n.bg-background\\\\/80 {\\n  background-color: rgb(var(--background) / 0.8);\\n}\\n.bg-black {\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(0 0 0 / var(--tw-bg-opacity, 1));\\n}\\n.bg-black\\\\/50 {\\n  background-color: rgb(0 0 0 / 0.5);\\n}\\n.bg-blue-100 {\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(219 234 254 / var(--tw-bg-opacity, 1));\\n}\\n.bg-blue-400\\\\/10 {\\n  background-color: rgb(96 165 250 / 0.1);\\n}\\n.bg-blue-50 {\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(239 246 255 / var(--tw-bg-opacity, 1));\\n}\\n.bg-blue-600 {\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(37 99 235 / var(--tw-bg-opacity, 1));\\n}\\n.bg-card {\\n  background-color: rgb(var(--card));\\n}\\n.bg-destructive {\\n  background-color: rgb(var(--destructive));\\n}\\n.bg-destructive\\\\/10 {\\n  background-color: rgb(var(--destructive) / 0.1);\\n}\\n.bg-gray-100 {\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(243 244 246 / var(--tw-bg-opacity, 1));\\n}\\n.bg-gray-200 {\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(229 231 235 / var(--tw-bg-opacity, 1));\\n}\\n.bg-gray-50 {\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(249 250 251 / var(--tw-bg-opacity, 1));\\n}\\n.bg-gray-50\\\\/50 {\\n  background-color: rgb(249 250 251 / 0.5);\\n}\\n.bg-green-100 {\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(220 252 231 / var(--tw-bg-opacity, 1));\\n}\\n.bg-green-50 {\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(240 253 244 / var(--tw-bg-opacity, 1));\\n}\\n.bg-green-50\\\\/50 {\\n  background-color: rgb(240 253 244 / 0.5);\\n}\\n.bg-green-600 {\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(22 163 74 / var(--tw-bg-opacity, 1));\\n}\\n.bg-input {\\n  background-color: rgb(var(--input));\\n}\\n.bg-muted {\\n  background-color: rgb(var(--muted));\\n}\\n.bg-popover {\\n  background-color: rgb(var(--popover));\\n}\\n.bg-primary {\\n  background-color: rgb(var(--primary));\\n}\\n.bg-primary\\\\/10 {\\n  background-color: rgb(var(--primary) / 0.1);\\n}\\n.bg-primary\\\\/20 {\\n  background-color: rgb(var(--primary) / 0.2);\\n}\\n.bg-primary\\\\/5 {\\n  background-color: rgb(var(--primary) / 0.05);\\n}\\n.bg-red-100 {\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(254 226 226 / var(--tw-bg-opacity, 1));\\n}\\n.bg-red-50 {\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(254 242 242 / var(--tw-bg-opacity, 1));\\n}\\n.bg-secondary {\\n  background-color: rgb(var(--secondary));\\n}\\n.bg-success {\\n  background-color: rgb(var(--success));\\n}\\n.bg-success\\\\/10 {\\n  background-color: rgb(var(--success) / 0.1);\\n}\\n.bg-transparent {\\n  background-color: transparent;\\n}\\n.bg-warning {\\n  background-color: rgb(var(--warning));\\n}\\n.bg-warning\\\\/10 {\\n  background-color: rgb(var(--warning) / 0.1);\\n}\\n.bg-white {\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(255 255 255 / var(--tw-bg-opacity, 1));\\n}\\n.bg-yellow-100 {\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(254 249 195 / var(--tw-bg-opacity, 1));\\n}\\n.bg-yellow-400\\\\/10 {\\n  background-color: rgb(250 204 21 / 0.1);\\n}\\n.bg-yellow-50 {\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(254 252 232 / var(--tw-bg-opacity, 1));\\n}\\n.bg-opacity-50 {\\n  --tw-bg-opacity: 0.5;\\n}\\n.bg-gradient-to-br {\\n  background-image: linear-gradient(to bottom right, var(--tw-gradient-stops));\\n}\\n.bg-gradient-to-r {\\n  background-image: linear-gradient(to right, var(--tw-gradient-stops));\\n}\\n.from-accent {\\n  --tw-gradient-from: rgb(var(--accent)) var(--tw-gradient-from-position);\\n  --tw-gradient-to: rgb(var(--accent) / 0) var(--tw-gradient-to-position);\\n  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);\\n}\\n.from-accent\\\\/5 {\\n  --tw-gradient-from: rgb(var(--accent) / 0.05) var(--tw-gradient-from-position);\\n  --tw-gradient-to: rgb(var(--accent) / 0) var(--tw-gradient-to-position);\\n  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);\\n}\\n.from-primary {\\n  --tw-gradient-from: rgb(var(--primary)) var(--tw-gradient-from-position);\\n  --tw-gradient-to: rgb(var(--primary) / 0) var(--tw-gradient-to-position);\\n  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);\\n}\\n.from-primary\\\\/5 {\\n  --tw-gradient-from: rgb(var(--primary) / 0.05) var(--tw-gradient-from-position);\\n  --tw-gradient-to: rgb(var(--primary) / 0) var(--tw-gradient-to-position);\\n  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);\\n}\\n.from-yellow-50 {\\n  --tw-gradient-from: #fefce8 var(--tw-gradient-from-position);\\n  --tw-gradient-to: rgb(254 252 232 / 0) var(--tw-gradient-to-position);\\n  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);\\n}\\n.via-accent\\\\/5 {\\n  --tw-gradient-to: rgb(var(--accent) / 0)  var(--tw-gradient-to-position);\\n  --tw-gradient-stops: var(--tw-gradient-from), rgb(var(--accent) / 0.05) var(--tw-gradient-via-position), var(--tw-gradient-to);\\n}\\n.via-primary\\\\/5 {\\n  --tw-gradient-to: rgb(var(--primary) / 0)  var(--tw-gradient-to-position);\\n  --tw-gradient-stops: var(--tw-gradient-from), rgb(var(--primary) / 0.05) var(--tw-gradient-via-position), var(--tw-gradient-to);\\n}\\n.to-accent {\\n  --tw-gradient-to: rgb(var(--accent)) var(--tw-gradient-to-position);\\n}\\n.to-accent\\\\/10 {\\n  --tw-gradient-to: rgb(var(--accent) / 0.1) var(--tw-gradient-to-position);\\n}\\n.to-accent\\\\/5 {\\n  --tw-gradient-to: rgb(var(--accent) / 0.05) var(--tw-gradient-to-position);\\n}\\n.to-accent\\\\/80 {\\n  --tw-gradient-to: rgb(var(--accent) / 0.8) var(--tw-gradient-to-position);\\n}\\n.to-orange-100 {\\n  --tw-gradient-to: #ffedd5 var(--tw-gradient-to-position);\\n}\\n.to-primary {\\n  --tw-gradient-to: rgb(var(--primary)) var(--tw-gradient-to-position);\\n}\\n.to-primary\\\\/10 {\\n  --tw-gradient-to: rgb(var(--primary) / 0.1) var(--tw-gradient-to-position);\\n}\\n.to-primary\\\\/80 {\\n  --tw-gradient-to: rgb(var(--primary) / 0.8) var(--tw-gradient-to-position);\\n}\\n.to-primary\\\\/90 {\\n  --tw-gradient-to: rgb(var(--primary) / 0.9) var(--tw-gradient-to-position);\\n}\\n.p-1 {\\n  padding: 0.25rem;\\n}\\n.p-1\\\\.5 {\\n  padding: 0.375rem;\\n}\\n.p-2 {\\n  padding: 0.5rem;\\n}\\n.p-3 {\\n  padding: 0.75rem;\\n}\\n.p-4 {\\n  padding: 1rem;\\n}\\n.p-6 {\\n  padding: 1.5rem;\\n}\\n.p-8 {\\n  padding: 2rem;\\n}\\n.px-1\\\\.5 {\\n  padding-left: 0.375rem;\\n  padding-right: 0.375rem;\\n}\\n.px-2 {\\n  padding-left: 0.5rem;\\n  padding-right: 0.5rem;\\n}\\n.px-2\\\\.5 {\\n  padding-left: 0.625rem;\\n  padding-right: 0.625rem;\\n}\\n.px-3 {\\n  padding-left: 0.75rem;\\n  padding-right: 0.75rem;\\n}\\n.px-4 {\\n  padding-left: 1rem;\\n  padding-right: 1rem;\\n}\\n.px-6 {\\n  padding-left: 1.5rem;\\n  padding-right: 1.5rem;\\n}\\n.px-8 {\\n  padding-left: 2rem;\\n  padding-right: 2rem;\\n}\\n.py-0\\\\.5 {\\n  padding-top: 0.125rem;\\n  padding-bottom: 0.125rem;\\n}\\n.py-1 {\\n  padding-top: 0.25rem;\\n  padding-bottom: 0.25rem;\\n}\\n.py-1\\\\.5 {\\n  padding-top: 0.375rem;\\n  padding-bottom: 0.375rem;\\n}\\n.py-16 {\\n  padding-top: 4rem;\\n  padding-bottom: 4rem;\\n}\\n.py-2 {\\n  padding-top: 0.5rem;\\n  padding-bottom: 0.5rem;\\n}\\n.py-2\\\\.5 {\\n  padding-top: 0.625rem;\\n  padding-bottom: 0.625rem;\\n}\\n.py-20 {\\n  padding-top: 5rem;\\n  padding-bottom: 5rem;\\n}\\n.py-3 {\\n  padding-top: 0.75rem;\\n  padding-bottom: 0.75rem;\\n}\\n.py-4 {\\n  padding-top: 1rem;\\n  padding-bottom: 1rem;\\n}\\n.pl-10 {\\n  padding-left: 2.5rem;\\n}\\n.pl-2 {\\n  padding-left: 0.5rem;\\n}\\n.pl-8 {\\n  padding-left: 2rem;\\n}\\n.pr-10 {\\n  padding-right: 2.5rem;\\n}\\n.pr-12 {\\n  padding-right: 3rem;\\n}\\n.pr-2 {\\n  padding-right: 0.5rem;\\n}\\n.pr-3 {\\n  padding-right: 0.75rem;\\n}\\n.pr-4 {\\n  padding-right: 1rem;\\n}\\n.pt-4 {\\n  padding-top: 1rem;\\n}\\n.pt-6 {\\n  padding-top: 1.5rem;\\n}\\n.text-center {\\n  text-align: center;\\n}\\n.text-2xl {\\n  font-size: 1.5rem;\\n  line-height: 2rem;\\n}\\n.text-3xl {\\n  font-size: 1.875rem;\\n  line-height: 2.25rem;\\n}\\n.text-lg {\\n  font-size: 1.125rem;\\n  line-height: 1.75rem;\\n}\\n.text-sm {\\n  font-size: 0.875rem;\\n  line-height: 1.25rem;\\n}\\n.text-xl {\\n  font-size: 1.25rem;\\n  line-height: 1.75rem;\\n}\\n.text-xs {\\n  font-size: 0.75rem;\\n  line-height: 1rem;\\n}\\n.font-bold {\\n  font-weight: 700;\\n}\\n.font-medium {\\n  font-weight: 500;\\n}\\n.font-semibold {\\n  font-weight: 600;\\n}\\n.uppercase {\\n  text-transform: uppercase;\\n}\\n.leading-none {\\n  line-height: 1;\\n}\\n.leading-relaxed {\\n  line-height: 1.625;\\n}\\n.leading-tight {\\n  line-height: 1.25;\\n}\\n.tracking-tight {\\n  letter-spacing: -0.025em;\\n}\\n.tracking-wider {\\n  letter-spacing: 0.05em;\\n}\\n.text-accent {\\n  color: rgb(var(--accent));\\n}\\n.text-accent-foreground {\\n  color: rgb(var(--accent-foreground));\\n}\\n.text-blue-400 {\\n  --tw-text-opacity: 1;\\n  color: rgb(96 165 250 / var(--tw-text-opacity, 1));\\n}\\n.text-blue-600 {\\n  --tw-text-opacity: 1;\\n  color: rgb(37 99 235 / var(--tw-text-opacity, 1));\\n}\\n.text-blue-800 {\\n  --tw-text-opacity: 1;\\n  color: rgb(30 64 175 / var(--tw-text-opacity, 1));\\n}\\n.text-destructive {\\n  color: rgb(var(--destructive));\\n}\\n.text-destructive-foreground {\\n  color: rgb(var(--destructive-foreground));\\n}\\n.text-foreground {\\n  color: rgb(var(--foreground));\\n}\\n.text-gray-400 {\\n  --tw-text-opacity: 1;\\n  color: rgb(156 163 175 / var(--tw-text-opacity, 1));\\n}\\n.text-gray-500 {\\n  --tw-text-opacity: 1;\\n  color: rgb(107 114 128 / var(--tw-text-opacity, 1));\\n}\\n.text-gray-600 {\\n  --tw-text-opacity: 1;\\n  color: rgb(75 85 99 / var(--tw-text-opacity, 1));\\n}\\n.text-gray-700 {\\n  --tw-text-opacity: 1;\\n  color: rgb(55 65 81 / var(--tw-text-opacity, 1));\\n}\\n.text-gray-800 {\\n  --tw-text-opacity: 1;\\n  color: rgb(31 41 55 / var(--tw-text-opacity, 1));\\n}\\n.text-gray-900 {\\n  --tw-text-opacity: 1;\\n  color: rgb(17 24 39 / var(--tw-text-opacity, 1));\\n}\\n.text-green-600 {\\n  --tw-text-opacity: 1;\\n  color: rgb(22 163 74 / var(--tw-text-opacity, 1));\\n}\\n.text-green-700 {\\n  --tw-text-opacity: 1;\\n  color: rgb(21 128 61 / var(--tw-text-opacity, 1));\\n}\\n.text-green-800 {\\n  --tw-text-opacity: 1;\\n  color: rgb(22 101 52 / var(--tw-text-opacity, 1));\\n}\\n.text-muted-foreground {\\n  color: rgb(var(--muted-foreground));\\n}\\n.text-popover-foreground {\\n  color: rgb(var(--popover-foreground));\\n}\\n.text-primary {\\n  color: rgb(var(--primary));\\n}\\n.text-primary-foreground {\\n  color: rgb(var(--primary-foreground));\\n}\\n.text-red-600 {\\n  --tw-text-opacity: 1;\\n  color: rgb(220 38 38 / var(--tw-text-opacity, 1));\\n}\\n.text-red-700 {\\n  --tw-text-opacity: 1;\\n  color: rgb(185 28 28 / var(--tw-text-opacity, 1));\\n}\\n.text-red-800 {\\n  --tw-text-opacity: 1;\\n  color: rgb(153 27 27 / var(--tw-text-opacity, 1));\\n}\\n.text-secondary-foreground {\\n  color: rgb(var(--secondary-foreground));\\n}\\n.text-success {\\n  color: rgb(var(--success));\\n}\\n.text-warning {\\n  color: rgb(var(--warning));\\n}\\n.text-white {\\n  --tw-text-opacity: 1;\\n  color: rgb(255 255 255 / var(--tw-text-opacity, 1));\\n}\\n.text-yellow-500 {\\n  --tw-text-opacity: 1;\\n  color: rgb(234 179 8 / var(--tw-text-opacity, 1));\\n}\\n.text-yellow-600 {\\n  --tw-text-opacity: 1;\\n  color: rgb(202 138 4 / var(--tw-text-opacity, 1));\\n}\\n.text-yellow-700 {\\n  --tw-text-opacity: 1;\\n  color: rgb(161 98 7 / var(--tw-text-opacity, 1));\\n}\\n.text-yellow-800 {\\n  --tw-text-opacity: 1;\\n  color: rgb(133 77 14 / var(--tw-text-opacity, 1));\\n}\\n.underline {\\n  text-decoration-line: underline;\\n}\\n.line-through {\\n  text-decoration-line: line-through;\\n}\\n.underline-offset-4 {\\n  text-underline-offset: 4px;\\n}\\n.placeholder-gray-500::-moz-placeholder {\\n  --tw-placeholder-opacity: 1;\\n  color: rgb(107 114 128 / var(--tw-placeholder-opacity, 1));\\n}\\n.placeholder-gray-500::placeholder {\\n  --tw-placeholder-opacity: 1;\\n  color: rgb(107 114 128 / var(--tw-placeholder-opacity, 1));\\n}\\n.opacity-0 {\\n  opacity: 0;\\n}\\n.opacity-100 {\\n  opacity: 1;\\n}\\n.opacity-50 {\\n  opacity: 0.5;\\n}\\n.opacity-70 {\\n  opacity: 0.7;\\n}\\n.shadow-2xl {\\n  --tw-shadow: 0 25px 50px -12px rgb(0 0 0 / 0.25);\\n  --tw-shadow-colored: 0 25px 50px -12px var(--tw-shadow-color);\\n  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);\\n}\\n.shadow-lg {\\n  --tw-shadow: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);\\n  --tw-shadow-colored: 0 10px 15px -3px var(--tw-shadow-color), 0 4px 6px -4px var(--tw-shadow-color);\\n  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);\\n}\\n.shadow-md {\\n  --tw-shadow: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);\\n  --tw-shadow-colored: 0 4px 6px -1px var(--tw-shadow-color), 0 2px 4px -2px var(--tw-shadow-color);\\n  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);\\n}\\n.shadow-sm {\\n  --tw-shadow: 0 1px 2px 0 rgb(0 0 0 / 0.05);\\n  --tw-shadow-colored: 0 1px 2px 0 var(--tw-shadow-color);\\n  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);\\n}\\n.shadow-xl {\\n  --tw-shadow: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1);\\n  --tw-shadow-colored: 0 20px 25px -5px var(--tw-shadow-color), 0 8px 10px -6px var(--tw-shadow-color);\\n  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);\\n}\\n.outline-none {\\n  outline: 2px solid transparent;\\n  outline-offset: 2px;\\n}\\n.outline {\\n  outline-style: solid;\\n}\\n.ring-offset-background {\\n  --tw-ring-offset-color: rgb(var(--background));\\n}\\n.filter {\\n  filter: var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow);\\n}\\n.backdrop-blur-sm {\\n  --tw-backdrop-blur: blur(4px);\\n  -webkit-backdrop-filter: var(--tw-backdrop-blur) var(--tw-backdrop-brightness) var(--tw-backdrop-contrast) var(--tw-backdrop-grayscale) var(--tw-backdrop-hue-rotate) var(--tw-backdrop-invert) var(--tw-backdrop-opacity) var(--tw-backdrop-saturate) var(--tw-backdrop-sepia);\\n  backdrop-filter: var(--tw-backdrop-blur) var(--tw-backdrop-brightness) var(--tw-backdrop-contrast) var(--tw-backdrop-grayscale) var(--tw-backdrop-hue-rotate) var(--tw-backdrop-invert) var(--tw-backdrop-opacity) var(--tw-backdrop-saturate) var(--tw-backdrop-sepia);\\n}\\n.transition {\\n  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter, -webkit-backdrop-filter;\\n  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter, backdrop-filter;\\n  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter, backdrop-filter, -webkit-backdrop-filter;\\n  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);\\n  transition-duration: 150ms;\\n}\\n.transition-all {\\n  transition-property: all;\\n  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);\\n  transition-duration: 150ms;\\n}\\n.transition-colors {\\n  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke;\\n  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);\\n  transition-duration: 150ms;\\n}\\n.transition-opacity {\\n  transition-property: opacity;\\n  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);\\n  transition-duration: 150ms;\\n}\\n.transition-transform {\\n  transition-property: transform;\\n  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);\\n  transition-duration: 150ms;\\n}\\n.duration-200 {\\n  transition-duration: 200ms;\\n}\\n.duration-300 {\\n  transition-duration: 300ms;\\n}\\n\\n.placeholder\\\\:text-muted-foreground::-moz-placeholder {\\n  color: rgb(var(--muted-foreground));\\n}\\n\\n.placeholder\\\\:text-muted-foreground::placeholder {\\n  color: rgb(var(--muted-foreground));\\n}\\n\\n.hover\\\\:border-accent:hover {\\n  border-color: rgb(var(--accent));\\n}\\n\\n.hover\\\\:bg-accent\\\\/10:hover {\\n  background-color: rgb(var(--accent) / 0.1);\\n}\\n\\n.hover\\\\:bg-accent\\\\/80:hover {\\n  background-color: rgb(var(--accent) / 0.8);\\n}\\n\\n.hover\\\\:bg-blue-700:hover {\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(29 78 216 / var(--tw-bg-opacity, 1));\\n}\\n\\n.hover\\\\:bg-destructive\\\\/10:hover {\\n  background-color: rgb(var(--destructive) / 0.1);\\n}\\n\\n.hover\\\\:bg-destructive\\\\/80:hover {\\n  background-color: rgb(var(--destructive) / 0.8);\\n}\\n\\n.hover\\\\:bg-destructive\\\\/90:hover {\\n  background-color: rgb(var(--destructive) / 0.9);\\n}\\n\\n.hover\\\\:bg-gray-100:hover {\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(243 244 246 / var(--tw-bg-opacity, 1));\\n}\\n\\n.hover\\\\:bg-gray-200:hover {\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(229 231 235 / var(--tw-bg-opacity, 1));\\n}\\n\\n.hover\\\\:bg-gray-50:hover {\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(249 250 251 / var(--tw-bg-opacity, 1));\\n}\\n\\n.hover\\\\:bg-green-700:hover {\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(21 128 61 / var(--tw-bg-opacity, 1));\\n}\\n\\n.hover\\\\:bg-muted:hover {\\n  background-color: rgb(var(--muted));\\n}\\n\\n.hover\\\\:bg-primary\\\\/10:hover {\\n  background-color: rgb(var(--primary) / 0.1);\\n}\\n\\n.hover\\\\:bg-primary\\\\/80:hover {\\n  background-color: rgb(var(--primary) / 0.8);\\n}\\n\\n.hover\\\\:bg-primary\\\\/90:hover {\\n  background-color: rgb(var(--primary) / 0.9);\\n}\\n\\n.hover\\\\:bg-red-50:hover {\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(254 242 242 / var(--tw-bg-opacity, 1));\\n}\\n\\n.hover\\\\:bg-secondary\\\\/80:hover {\\n  background-color: rgb(var(--secondary) / 0.8);\\n}\\n\\n.hover\\\\:bg-success\\\\/80:hover {\\n  background-color: rgb(var(--success) / 0.8);\\n}\\n\\n.hover\\\\:bg-success\\\\/90:hover {\\n  background-color: rgb(var(--success) / 0.9);\\n}\\n\\n.hover\\\\:bg-warning\\\\/80:hover {\\n  background-color: rgb(var(--warning) / 0.8);\\n}\\n\\n.hover\\\\:bg-warning\\\\/90:hover {\\n  background-color: rgb(var(--warning) / 0.9);\\n}\\n\\n.hover\\\\:from-primary\\\\/90:hover {\\n  --tw-gradient-from: rgb(var(--primary) / 0.9) var(--tw-gradient-from-position);\\n  --tw-gradient-to: rgb(var(--primary) / 0) var(--tw-gradient-to-position);\\n  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);\\n}\\n\\n.hover\\\\:to-primary:hover {\\n  --tw-gradient-to: rgb(var(--primary)) var(--tw-gradient-to-position);\\n}\\n\\n.hover\\\\:text-accent-foreground:hover {\\n  color: rgb(var(--accent-foreground));\\n}\\n\\n.hover\\\\:text-blue-700:hover {\\n  --tw-text-opacity: 1;\\n  color: rgb(29 78 216 / var(--tw-text-opacity, 1));\\n}\\n\\n.hover\\\\:text-destructive:hover {\\n  color: rgb(var(--destructive));\\n}\\n\\n.hover\\\\:text-foreground:hover {\\n  color: rgb(var(--foreground));\\n}\\n\\n.hover\\\\:text-gray-600:hover {\\n  --tw-text-opacity: 1;\\n  color: rgb(75 85 99 / var(--tw-text-opacity, 1));\\n}\\n\\n.hover\\\\:text-green-500:hover {\\n  --tw-text-opacity: 1;\\n  color: rgb(34 197 94 / var(--tw-text-opacity, 1));\\n}\\n\\n.hover\\\\:text-primary\\\\/80:hover {\\n  color: rgb(var(--primary) / 0.8);\\n}\\n\\n.hover\\\\:text-red-500:hover {\\n  --tw-text-opacity: 1;\\n  color: rgb(239 68 68 / var(--tw-text-opacity, 1));\\n}\\n\\n.hover\\\\:text-yellow-800:hover {\\n  --tw-text-opacity: 1;\\n  color: rgb(133 77 14 / var(--tw-text-opacity, 1));\\n}\\n\\n.hover\\\\:underline:hover {\\n  text-decoration-line: underline;\\n}\\n\\n.hover\\\\:opacity-100:hover {\\n  opacity: 1;\\n}\\n\\n.hover\\\\:shadow-lg:hover {\\n  --tw-shadow: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);\\n  --tw-shadow-colored: 0 10px 15px -3px var(--tw-shadow-color), 0 4px 6px -4px var(--tw-shadow-color);\\n  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);\\n}\\n\\n.hover\\\\:shadow-md:hover {\\n  --tw-shadow: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);\\n  --tw-shadow-colored: 0 4px 6px -1px var(--tw-shadow-color), 0 2px 4px -2px var(--tw-shadow-color);\\n  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);\\n}\\n\\n.focus\\\\:border-destructive:focus {\\n  border-color: rgb(var(--destructive));\\n}\\n\\n.focus\\\\:border-primary:focus {\\n  border-color: rgb(var(--primary));\\n}\\n\\n.focus\\\\:border-transparent:focus {\\n  border-color: transparent;\\n}\\n\\n.focus\\\\:bg-accent:focus {\\n  background-color: rgb(var(--accent));\\n}\\n\\n.focus\\\\:text-accent-foreground:focus {\\n  color: rgb(var(--accent-foreground));\\n}\\n\\n.focus\\\\:outline-none:focus {\\n  outline: 2px solid transparent;\\n  outline-offset: 2px;\\n}\\n\\n.focus\\\\:ring-2:focus {\\n  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);\\n  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color);\\n  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);\\n}\\n\\n.focus\\\\:ring-blue-500:focus {\\n  --tw-ring-opacity: 1;\\n  --tw-ring-color: rgb(59 130 246 / var(--tw-ring-opacity, 1));\\n}\\n\\n.focus\\\\:ring-green-500:focus {\\n  --tw-ring-opacity: 1;\\n  --tw-ring-color: rgb(34 197 94 / var(--tw-ring-opacity, 1));\\n}\\n\\n.focus\\\\:ring-primary:focus {\\n  --tw-ring-color: rgb(var(--primary));\\n}\\n\\n.focus\\\\:ring-ring:focus {\\n  --tw-ring-color: rgb(var(--ring));\\n}\\n\\n.focus\\\\:ring-offset-2:focus {\\n  --tw-ring-offset-width: 2px;\\n}\\n\\n.focus-visible\\\\:border-primary:focus-visible {\\n  border-color: rgb(var(--primary));\\n}\\n\\n.focus-visible\\\\:outline-none:focus-visible {\\n  outline: 2px solid transparent;\\n  outline-offset: 2px;\\n}\\n\\n.focus-visible\\\\:ring-2:focus-visible {\\n  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);\\n  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color);\\n  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);\\n}\\n\\n.focus-visible\\\\:ring-ring:focus-visible {\\n  --tw-ring-color: rgb(var(--ring));\\n}\\n\\n.focus-visible\\\\:ring-offset-2:focus-visible {\\n  --tw-ring-offset-width: 2px;\\n}\\n\\n.active\\\\:cursor-grabbing:active {\\n  cursor: grabbing;\\n}\\n\\n.active\\\\:bg-primary\\\\/95:active {\\n  background-color: rgb(var(--primary) / 0.95);\\n}\\n\\n.disabled\\\\:pointer-events-none:disabled {\\n  pointer-events: none;\\n}\\n\\n.disabled\\\\:cursor-not-allowed:disabled {\\n  cursor: not-allowed;\\n}\\n\\n.disabled\\\\:opacity-50:disabled {\\n  opacity: 0.5;\\n}\\n\\n.group:hover .group-hover\\\\:bg-blue-400\\\\/20 {\\n  background-color: rgb(96 165 250 / 0.2);\\n}\\n\\n.group:hover .group-hover\\\\:bg-yellow-400\\\\/20 {\\n  background-color: rgb(250 204 21 / 0.2);\\n}\\n\\n.group:hover .group-hover\\\\:opacity-100 {\\n  opacity: 1;\\n}\\n\\n.data-\\\\[disabled\\\\]\\\\:pointer-events-none[data-disabled] {\\n  pointer-events: none;\\n}\\n\\n.data-\\\\[side\\\\=bottom\\\\]\\\\:translate-y-1[data-side=\\\"bottom\\\"] {\\n  --tw-translate-y: 0.25rem;\\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\\n}\\n\\n.data-\\\\[side\\\\=left\\\\]\\\\:-translate-x-1[data-side=\\\"left\\\"] {\\n  --tw-translate-x: -0.25rem;\\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\\n}\\n\\n.data-\\\\[side\\\\=right\\\\]\\\\:translate-x-1[data-side=\\\"right\\\"] {\\n  --tw-translate-x: 0.25rem;\\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\\n}\\n\\n.data-\\\\[side\\\\=top\\\\]\\\\:-translate-y-1[data-side=\\\"top\\\"] {\\n  --tw-translate-y: -0.25rem;\\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\\n}\\n\\n.data-\\\\[state\\\\=open\\\\]\\\\:bg-accent[data-state=\\\"open\\\"] {\\n  background-color: rgb(var(--accent));\\n}\\n\\n.data-\\\\[state\\\\=open\\\\]\\\\:text-muted-foreground[data-state=\\\"open\\\"] {\\n  color: rgb(var(--muted-foreground));\\n}\\n\\n.data-\\\\[disabled\\\\]\\\\:opacity-50[data-disabled] {\\n  opacity: 0.5;\\n}\\n\\n.dark\\\\:border-accent\\\\/40:is(.dark *) {\\n  border-color: rgb(var(--accent) / 0.4);\\n}\\n\\n.dark\\\\:border-gray-600:is(.dark *) {\\n  --tw-border-opacity: 1;\\n  border-color: rgb(75 85 99 / var(--tw-border-opacity, 1));\\n}\\n\\n.dark\\\\:border-gray-700:is(.dark *) {\\n  --tw-border-opacity: 1;\\n  border-color: rgb(55 65 81 / var(--tw-border-opacity, 1));\\n}\\n\\n.dark\\\\:border-green-700:is(.dark *) {\\n  --tw-border-opacity: 1;\\n  border-color: rgb(21 128 61 / var(--tw-border-opacity, 1));\\n}\\n\\n.dark\\\\:border-primary\\\\/30:is(.dark *) {\\n  border-color: rgb(var(--primary) / 0.3);\\n}\\n\\n.dark\\\\:border-red-700:is(.dark *) {\\n  --tw-border-opacity: 1;\\n  border-color: rgb(185 28 28 / var(--tw-border-opacity, 1));\\n}\\n\\n.dark\\\\:border-red-800:is(.dark *) {\\n  --tw-border-opacity: 1;\\n  border-color: rgb(153 27 27 / var(--tw-border-opacity, 1));\\n}\\n\\n.dark\\\\:border-yellow-700:is(.dark *) {\\n  --tw-border-opacity: 1;\\n  border-color: rgb(161 98 7 / var(--tw-border-opacity, 1));\\n}\\n\\n.dark\\\\:bg-accent\\\\/20:is(.dark *) {\\n  background-color: rgb(var(--accent) / 0.2);\\n}\\n\\n.dark\\\\:bg-gray-700:is(.dark *) {\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(55 65 81 / var(--tw-bg-opacity, 1));\\n}\\n\\n.dark\\\\:bg-gray-800:is(.dark *) {\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(31 41 55 / var(--tw-bg-opacity, 1));\\n}\\n\\n.dark\\\\:bg-gray-800\\\\/30:is(.dark *) {\\n  background-color: rgb(31 41 55 / 0.3);\\n}\\n\\n.dark\\\\:bg-gray-800\\\\/50:is(.dark *) {\\n  background-color: rgb(31 41 55 / 0.5);\\n}\\n\\n.dark\\\\:bg-gray-900:is(.dark *) {\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(17 24 39 / var(--tw-bg-opacity, 1));\\n}\\n\\n.dark\\\\:bg-gray-900\\\\/50:is(.dark *) {\\n  background-color: rgb(17 24 39 / 0.5);\\n}\\n\\n.dark\\\\:bg-green-900\\\\/20:is(.dark *) {\\n  background-color: rgb(20 83 45 / 0.2);\\n}\\n\\n.dark\\\\:bg-green-900\\\\/30:is(.dark *) {\\n  background-color: rgb(20 83 45 / 0.3);\\n}\\n\\n.dark\\\\:bg-primary\\\\/10:is(.dark *) {\\n  background-color: rgb(var(--primary) / 0.1);\\n}\\n\\n.dark\\\\:bg-primary\\\\/20:is(.dark *) {\\n  background-color: rgb(var(--primary) / 0.2);\\n}\\n\\n.dark\\\\:bg-red-900\\\\/20:is(.dark *) {\\n  background-color: rgb(127 29 29 / 0.2);\\n}\\n\\n.dark\\\\:bg-red-900\\\\/30:is(.dark *) {\\n  background-color: rgb(127 29 29 / 0.3);\\n}\\n\\n.dark\\\\:bg-yellow-900\\\\/30:is(.dark *) {\\n  background-color: rgb(113 63 18 / 0.3);\\n}\\n\\n.dark\\\\:from-gray-900:is(.dark *) {\\n  --tw-gradient-from: #111827 var(--tw-gradient-from-position);\\n  --tw-gradient-to: rgb(17 24 39 / 0) var(--tw-gradient-to-position);\\n  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);\\n}\\n\\n.dark\\\\:from-primary\\\\/10:is(.dark *) {\\n  --tw-gradient-from: rgb(var(--primary) / 0.1) var(--tw-gradient-from-position);\\n  --tw-gradient-to: rgb(var(--primary) / 0) var(--tw-gradient-to-position);\\n  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);\\n}\\n\\n.dark\\\\:via-gray-800:is(.dark *) {\\n  --tw-gradient-to: rgb(31 41 55 / 0)  var(--tw-gradient-to-position);\\n  --tw-gradient-stops: var(--tw-gradient-from), #1f2937 var(--tw-gradient-via-position), var(--tw-gradient-to);\\n}\\n\\n.dark\\\\:to-accent\\\\/10:is(.dark *) {\\n  --tw-gradient-to: rgb(var(--accent) / 0.1) var(--tw-gradient-to-position);\\n}\\n\\n.dark\\\\:to-gray-900:is(.dark *) {\\n  --tw-gradient-to: #111827 var(--tw-gradient-to-position);\\n}\\n\\n.dark\\\\:text-gray-300:is(.dark *) {\\n  --tw-text-opacity: 1;\\n  color: rgb(209 213 219 / var(--tw-text-opacity, 1));\\n}\\n\\n.dark\\\\:text-gray-400:is(.dark *) {\\n  --tw-text-opacity: 1;\\n  color: rgb(156 163 175 / var(--tw-text-opacity, 1));\\n}\\n\\n.dark\\\\:text-gray-500:is(.dark *) {\\n  --tw-text-opacity: 1;\\n  color: rgb(107 114 128 / var(--tw-text-opacity, 1));\\n}\\n\\n.dark\\\\:text-green-300:is(.dark *) {\\n  --tw-text-opacity: 1;\\n  color: rgb(134 239 172 / var(--tw-text-opacity, 1));\\n}\\n\\n.dark\\\\:text-red-300:is(.dark *) {\\n  --tw-text-opacity: 1;\\n  color: rgb(252 165 165 / var(--tw-text-opacity, 1));\\n}\\n\\n.dark\\\\:text-red-400:is(.dark *) {\\n  --tw-text-opacity: 1;\\n  color: rgb(248 113 113 / var(--tw-text-opacity, 1));\\n}\\n\\n.dark\\\\:text-white:is(.dark *) {\\n  --tw-text-opacity: 1;\\n  color: rgb(255 255 255 / var(--tw-text-opacity, 1));\\n}\\n\\n.dark\\\\:text-yellow-300:is(.dark *) {\\n  --tw-text-opacity: 1;\\n  color: rgb(253 224 71 / var(--tw-text-opacity, 1));\\n}\\n\\n.dark\\\\:placeholder-gray-400:is(.dark *)::-moz-placeholder {\\n  --tw-placeholder-opacity: 1;\\n  color: rgb(156 163 175 / var(--tw-placeholder-opacity, 1));\\n}\\n\\n.dark\\\\:placeholder-gray-400:is(.dark *)::placeholder {\\n  --tw-placeholder-opacity: 1;\\n  color: rgb(156 163 175 / var(--tw-placeholder-opacity, 1));\\n}\\n\\n.dark\\\\:hover\\\\:bg-gray-600:hover:is(.dark *) {\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(75 85 99 / var(--tw-bg-opacity, 1));\\n}\\n\\n.dark\\\\:hover\\\\:bg-gray-700:hover:is(.dark *) {\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(55 65 81 / var(--tw-bg-opacity, 1));\\n}\\n\\n.dark\\\\:hover\\\\:bg-red-900\\\\/20:hover:is(.dark *) {\\n  background-color: rgb(127 29 29 / 0.2);\\n}\\n\\n.dark\\\\:hover\\\\:text-gray-300:hover:is(.dark *) {\\n  --tw-text-opacity: 1;\\n  color: rgb(209 213 219 / var(--tw-text-opacity, 1));\\n}\\n\\n.dark\\\\:hover\\\\:text-red-400:hover:is(.dark *) {\\n  --tw-text-opacity: 1;\\n  color: rgb(248 113 113 / var(--tw-text-opacity, 1));\\n}\\n\\n.dark\\\\:hover\\\\:shadow-xl:hover:is(.dark *) {\\n  --tw-shadow: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1);\\n  --tw-shadow-colored: 0 20px 25px -5px var(--tw-shadow-color), 0 8px 10px -6px var(--tw-shadow-color);\\n  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);\\n}\\n\\n@media (min-width: 640px) {\\n\\n  .sm\\\\:max-w-\\\\[500px\\\\] {\\n    max-width: 500px;\\n  }\\n\\n  .sm\\\\:flex-row {\\n    flex-direction: row;\\n  }\\n\\n  .sm\\\\:justify-end {\\n    justify-content: flex-end;\\n  }\\n\\n  .sm\\\\:space-x-2 > :not([hidden]) ~ :not([hidden]) {\\n    --tw-space-x-reverse: 0;\\n    margin-right: calc(0.5rem * var(--tw-space-x-reverse));\\n    margin-left: calc(0.5rem * calc(1 - var(--tw-space-x-reverse)));\\n  }\\n\\n  .sm\\\\:text-left {\\n    text-align: left;\\n  }\\n}\\n\\n@media (min-width: 768px) {\\n\\n  .md\\\\:grid-cols-3 {\\n    grid-template-columns: repeat(3, minmax(0, 1fr));\\n  }\\n}\\n\\n.\\\\[\\\\&\\\\>span\\\\]\\\\:line-clamp-1>span {\\n  overflow: hidden;\\n  display: -webkit-box;\\n  -webkit-box-orient: vertical;\\n  -webkit-line-clamp: 1;\\n}\\n\", \"\",{\"version\":3,\"sources\":[\"webpack://styles/globals.css\"],\"names\":[],\"mappings\":\"AAAA;EAAA,wBAAc;EAAd,wBAAc;EAAd,mBAAc;EAAd,mBAAc;EAAd,cAAc;EAAd,cAAc;EAAd,cAAc;EAAd,eAAc;EAAd,eAAc;EAAd,aAAc;EAAd,aAAc;EAAd,kBAAc;EAAd,sCAAc;EAAd,8BAAc;EAAd,6BAAc;EAAd,4BAAc;EAAd,eAAc;EAAd,oBAAc;EAAd,sBAAc;EAAd,uBAAc;EAAd,wBAAc;EAAd,kBAAc;EAAd,2BAAc;EAAd,4BAAc;EAAd,sCAAc;EAAd,kCAAc;EAAd,2BAAc;EAAd,sBAAc;EAAd,8BAAc;EAAd,YAAc;EAAd,kBAAc;EAAd,gBAAc;EAAd,iBAAc;EAAd,kBAAc;EAAd,cAAc;EAAd,gBAAc;EAAd,aAAc;EAAd,mBAAc;EAAd,qBAAc;EAAd,2BAAc;EAAd,yBAAc;EAAd,0BAAc;EAAd,2BAAc;EAAd,uBAAc;EAAd,wBAAc;EAAd,yBAAc;EAAd,sBAAc;EAAd,oBAAc;EAAd,sBAAc;EAAd,qBAAc;EAAd;AAAc;;AAAd;EAAA,wBAAc;EAAd,wBAAc;EAAd,mBAAc;EAAd,mBAAc;EAAd,cAAc;EAAd,cAAc;EAAd,cAAc;EAAd,eAAc;EAAd,eAAc;EAAd,aAAc;EAAd,aAAc;EAAd,kBAAc;EAAd,sCAAc;EAAd,8BAAc;EAAd,6BAAc;EAAd,4BAAc;EAAd,eAAc;EAAd,oBAAc;EAAd,sBAAc;EAAd,uBAAc;EAAd,wBAAc;EAAd,kBAAc;EAAd,2BAAc;EAAd,4BAAc;EAAd,sCAAc;EAAd,kCAAc;EAAd,2BAAc;EAAd,sBAAc;EAAd,8BAAc;EAAd,YAAc;EAAd,kBAAc;EAAd,gBAAc;EAAd,iBAAc;EAAd,kBAAc;EAAd,cAAc;EAAd,gBAAc;EAAd,aAAc;EAAd,mBAAc;EAAd,qBAAc;EAAd,2BAAc;EAAd,yBAAc;EAAd,0BAAc;EAAd,2BAAc;EAAd,uBAAc;EAAd,wBAAc;EAAd,yBAAc;EAAd,sBAAc;EAAd,oBAAc;EAAd,sBAAc;EAAd,qBAAc;EAAd;AAAc,CAAd;;CAAc,CAAd;;;CAAc;;AAAd;;;EAAA,sBAAc,EAAd,MAAc;EAAd,eAAc,EAAd,MAAc;EAAd,mBAAc,EAAd,MAAc;EAAd,qBAAc,EAAd,MAAc;AAAA;;AAAd;;EAAA,gBAAc;AAAA;;AAAd;;;;;;;;CAAc;;AAAd;;EAAA,gBAAc,EAAd,MAAc;EAAd,8BAAc,EAAd,MAAc;EAAd,gBAAc,EAAd,MAAc;EAAd,cAAc;KAAd,WAAc,EAAd,MAAc;EAAd,+HAAc,EAAd,MAAc;EAAd,6BAAc,EAAd,MAAc;EAAd,+BAAc,EAAd,MAAc;EAAd,wCAAc,EAAd,MAAc;AAAA;;AAAd;;;CAAc;;AAAd;EAAA,SAAc,EAAd,MAAc;EAAd,oBAAc,EAAd,MAAc;AAAA;;AAAd;;;;CAAc;;AAAd;EAAA,SAAc,EAAd,MAAc;EAAd,cAAc,EAAd,MAAc;EAAd,qBAAc,EAAd,MAAc;AAAA;;AAAd;;CAAc;;AAAd;EAAA,yCAAc;UAAd,iCAAc;AAAA;;AAAd;;CAAc;;AAAd;;;;;;EAAA,kBAAc;EAAd,oBAAc;AAAA;;AAAd;;CAAc;;AAAd;EAAA,cAAc;EAAd,wBAAc;AAAA;;AAAd;;CAAc;;AAAd;;EAAA,mBAAc;AAAA;;AAAd;;;;;CAAc;;AAAd;;;;EAAA,+GAAc,EAAd,MAAc;EAAd,6BAAc,EAAd,MAAc;EAAd,+BAAc,EAAd,MAAc;EAAd,cAAc,EAAd,MAAc;AAAA;;AAAd;;CAAc;;AAAd;EAAA,cAAc;AAAA;;AAAd;;CAAc;;AAAd;;EAAA,cAAc;EAAd,cAAc;EAAd,kBAAc;EAAd,wBAAc;AAAA;;AAAd;EAAA,eAAc;AAAA;;AAAd;EAAA,WAAc;AAAA;;AAAd;;;;CAAc;;AAAd;EAAA,cAAc,EAAd,MAAc;EAAd,qBAAc,EAAd,MAAc;EAAd,yBAAc,EAAd,MAAc;AAAA;;AAAd;;;;CAAc;;AAAd;;;;;EAAA,oBAAc,EAAd,MAAc;EAAd,8BAAc,EAAd,MAAc;EAAd,gCAAc,EAAd,MAAc;EAAd,eAAc,EAAd,MAAc;EAAd,oBAAc,EAAd,MAAc;EAAd,oBAAc,EAAd,MAAc;EAAd,uBAAc,EAAd,MAAc;EAAd,cAAc,EAAd,MAAc;EAAd,SAAc,EAAd,MAAc;EAAd,UAAc,EAAd,MAAc;AAAA;;AAAd;;CAAc;;AAAd;;EAAA,oBAAc;AAAA;;AAAd;;;CAAc;;AAAd;;;;EAAA,0BAAc,EAAd,MAAc;EAAd,6BAAc,EAAd,MAAc;EAAd,sBAAc,EAAd,MAAc;AAAA;;AAAd;;CAAc;;AAAd;EAAA,aAAc;AAAA;;AAAd;;CAAc;;AAAd;EAAA,gBAAc;AAAA;;AAAd;;CAAc;;AAAd;EAAA,wBAAc;AAAA;;AAAd;;CAAc;;AAAd;;EAAA,YAAc;AAAA;;AAAd;;;CAAc;;AAAd;EAAA,6BAAc,EAAd,MAAc;EAAd,oBAAc,EAAd,MAAc;AAAA;;AAAd;;CAAc;;AAAd;EAAA,wBAAc;AAAA;;AAAd;;;CAAc;;AAAd;EAAA,0BAAc,EAAd,MAAc;EAAd,aAAc,EAAd,MAAc;AAAA;;AAAd;;CAAc;;AAAd;EAAA,kBAAc;AAAA;;AAAd;;CAAc;;AAAd;;;;;;;;;;;;;EAAA,SAAc;AAAA;;AAAd;EAAA,SAAc;EAAd,UAAc;AAAA;;AAAd;EAAA,UAAc;AAAA;;AAAd;;;EAAA,gBAAc;EAAd,SAAc;EAAd,UAAc;AAAA;;AAAd;;CAAc;AAAd;EAAA,UAAc;AAAA;;AAAd;;CAAc;;AAAd;EAAA,gBAAc;AAAA;;AAAd;;;CAAc;;AAAd;EAAA,UAAc,EAAd,MAAc;EAAd,cAAc,EAAd,MAAc;AAAA;;AAAd;;EAAA,UAAc,EAAd,MAAc;EAAd,cAAc,EAAd,MAAc;AAAA;;AAAd;;CAAc;;AAAd;;EAAA,eAAc;AAAA;;AAAd;;CAAc;AAAd;EAAA,eAAc;AAAA;;AAAd;;;;CAAc;;AAAd;;;;;;;;EAAA,cAAc,EAAd,MAAc;EAAd,sBAAc,EAAd,MAAc;AAAA;;AAAd;;CAAc;;AAAd;;EAAA,eAAc;EAAd,YAAc;AAAA;;AAAd,wEAAc;AAAd;EAAA,aAAc;AAAA;EAAd;IAAA,gCAAc;IAAd,yBAAc,QAAd,sCAAc;IAAd,sBAAc,WAAd,6BAAc;IAAd,mBAAc,cAAd,0BAAc;IAAd,2BAAc,MAAd,kCAAc;IAAd,sBAAc,WAAd,4BAAc;IAAd,8BAAc,GAAd,qCAAc;IAAd,qBAAc,YAAd,mCAAc;IAAd,iCAAc,EAAd,oCAAc;IAAd,wBAAc,SAAd,mCAAc;IAAd,gCAAc,EAAd,uCAAc;IAAd,oBAAc,aAAd,oCAAc;IAAd,+BAAc,EAAd,qCAAc;IAAd,qBAAc,YAAd,gCAAc;IAAd,6BAAc,IAAd,oCAAc;IAAd,wBAAc,SAAd,kCAAc;IAAd,qCAAc,EAAd,wCAAc;IAAd,qBAAc,YAAd,gCAAc;IAAd,oBAAc,aAAd,oCAAc;IAAd,kBAAc,eAAd,8BAAc;IAAd,gBAAc;IAAd,oBAAc,aAAd,gCAAc;IAAd,qBAAc,YAAd,gCAAc;EAAA;EAAd;EAAA;AAAc;EAAd;EAAA,wCAAc;EAAd,6BAAc;IAAd;AAAc;AAoEZ,uCAAuC;AAMrC;EAAA,4BAAqQ;EAArQ,qCAAqQ;EAArQ,kBAAqQ;EAArQ,mBAAqQ;EAArQ,qBAAqQ;EAArQ,wBAAqQ;EAArQ,gBAAqQ;EAArQ,qCAAqQ;EAArQ,wBAAqQ;EAArQ,wDAAqQ;EAArQ;AAAqQ;AAArQ;EAAA;AAAqQ;AAArQ;EAAA,8BAAqQ;EAArQ,mBAAqQ;EAArQ,2GAAqQ;EAArQ,yGAAqQ;EAArQ,4FAAqQ;EAArQ,iCAAqQ;EAArQ;AAAqQ;AAArQ;EAAA;AAAqQ;AAArQ;EAAA,mBAAqQ;EAArQ;AAAqQ;AAYrQ;EAAA,4BAA8M;EAA9M,iBAA8M;EAA9M,gCAA8M;EAA9M,mCAA8M;EAA9M,qBAA8M;EAA9M,sBAA8M;EAA9M,qBAA8M;EAA9M,wBAA8M;EAA9M,6BAA8M;EAA9M,wBAA8M;EAA9M,wDAA8M;EAA9M;AAA8M;AAA9M;EAAA;AAA8M;AAA9M;EAAA;AAA8M;AAA9M;EAAA,iCAA8M;EAA9M,8BAA8M;EAA9M,mBAA8M;EAA9M,2GAA8M;EAA9M,yGAA8M;EAA9M,4FAA8M;EAA9M;AAA8M;AAQ9M;EAAA,uBAAsD;EAAtD,gCAAsD;EAAtD,kCAAsD;EAAtD,6BAAsD;EAAtD,+QAAsD;EAAtD;AAAsD;AAItD;EAAA,eAA+I;EAA/I,sBAA+I;EAA/I,iBAA+I;EAA/I,gCAA+I;EAA/I,kCAA+I;EAA/I,aAA+I;EAA/I,0CAA+I;EAA/I,uDAA+I;EAA/I,uGAA+I;EAA/I,wBAA+I;EAA/I,wDAA+I;EAA/I;AAA+I;AAA/I;EAAA,uCAA+I;EAA/I,6EAA+I;EAA/I,iGAA+I;EAA/I;AAA+I;AAmCjJ;IACE,qBAAqB;IACrB,8CAA8C;EAChD;AAEA;IACE,UAAU;EACZ;AAEA;IACE,uBAAuB;EACzB;AAEA;IACE,mCAAmC;IACnC,kBAAkB;EACpB;AAEA;IACE,8CAA8C;EAChD;AAvJF;EAAA,kBAAmB;EAAnB,UAAmB;EAAnB,WAAmB;EAAnB,UAAmB;EAAnB,YAAmB;EAAnB,gBAAmB;EAAnB,sBAAmB;EAAnB,mBAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,QAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,qBAAmB;EAAnB;AAAmB;AAAnB;EAAA,iBAAmB;EAAnB;AAAmB;AAAnB;EAAA,iBAAmB;EAAnB;AAAmB;AAAnB;EAAA,mBAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,gBAAmB;EAAnB,oBAAmB;EAAnB,4BAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,sBAAmB;EAAnB;AAAmB;AAAnB;EAAA,sBAAmB;EAAnB;AAAmB;AAAnB;EAAA,sBAAmB;EAAnB;AAAmB;AAAnB;EAAA,mBAAmB;EAAnB;AAAmB;AAAnB;EAAA,iBAAmB;EAAnB;AAAmB;AAAnB;EAAA,iBAAmB;EAAnB;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA,eAAmB;EAAnB,eAAmB;EAAnB;AAAmB;AAAnB;EAAA,eAAmB;EAAnB,eAAmB;EAAnB;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;;EAAA;IAAA,2BAAmB;IAAnB;EAAmB;;EAAnB;IAAA,eAAmB;IAAnB;EAAmB;AAAA;AAAnB;EAAA;AAAmB;AAAnB;;EAAA;IAAA;EAAmB;AAAA;AAAnB;EAAA;AAAmB;AAAnB;;EAAA;IAAA;EAAmB;AAAA;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,yBAAmB;KAAnB,sBAAmB;UAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,uBAAmB;EAAnB,uDAAmB;EAAnB;AAAmB;AAAnB;EAAA,uBAAmB;EAAnB,sDAAmB;EAAnB;AAAmB;AAAnB;EAAA,uBAAmB;EAAnB,uDAAmB;EAAnB;AAAmB;AAAnB;EAAA,uBAAmB;EAAnB,oDAAmB;EAAnB;AAAmB;AAAnB;EAAA,uBAAmB;EAAnB,+DAAmB;EAAnB;AAAmB;AAAnB;EAAA,uBAAmB;EAAnB,gEAAmB;EAAnB;AAAmB;AAAnB;EAAA,uBAAmB;EAAnB,8DAAmB;EAAnB;AAAmB;AAAnB;EAAA,uBAAmB;EAAnB,+DAAmB;EAAnB;AAAmB;AAAnB;EAAA,uBAAmB;EAAnB,4DAAmB;EAAnB;AAAmB;AAAnB;EAAA,uBAAmB;EAAnB,+DAAmB;EAAnB;AAAmB;AAAnB;EAAA,uBAAmB;EAAnB,8DAAmB;EAAnB;AAAmB;AAAnB;EAAA,uBAAmB;EAAnB,4DAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,+BAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,sBAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,sBAAmB;EAAnB;AAAmB;AAAnB;EAAA,sBAAmB;EAAnB;AAAmB;AAAnB;EAAA,sBAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,sBAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,sBAAmB;EAAnB;AAAmB;AAAnB;EAAA,sBAAmB;EAAnB;AAAmB;AAAnB;EAAA,sBAAmB;EAAnB;AAAmB;AAAnB;EAAA,sBAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,uEAAmB;EAAnB,uEAAmB;EAAnB;AAAmB;AAAnB;EAAA,8EAAmB;EAAnB,uEAAmB;EAAnB;AAAmB;AAAnB;EAAA,wEAAmB;EAAnB,wEAAmB;EAAnB;AAAmB;AAAnB;EAAA,+EAAmB;EAAnB,wEAAmB;EAAnB;AAAmB;AAAnB;EAAA,4DAAmB;EAAnB,qEAAmB;EAAnB;AAAmB;AAAnB;EAAA,wEAAmB;EAAnB;AAAmB;AAAnB;EAAA,yEAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,sBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,sBAAmB;EAAnB;AAAmB;AAAnB;EAAA,qBAAmB;EAAnB;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA,qBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,qBAAmB;EAAnB;AAAmB;AAAnB;EAAA,iBAAmB;EAAnB;AAAmB;AAAnB;EAAA,mBAAmB;EAAnB;AAAmB;AAAnB;EAAA,qBAAmB;EAAnB;AAAmB;AAAnB;EAAA,iBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,iBAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,iBAAmB;EAAnB;AAAmB;AAAnB;EAAA,mBAAmB;EAAnB;AAAmB;AAAnB;EAAA,mBAAmB;EAAnB;AAAmB;AAAnB;EAAA,mBAAmB;EAAnB;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,2BAAmB;EAAnB;AAAmB;AAAnB;EAAA,2BAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,gDAAmB;EAAnB,6DAAmB;EAAnB;AAAmB;AAAnB;EAAA,+EAAmB;EAAnB,mGAAmB;EAAnB;AAAmB;AAAnB;EAAA,6EAAmB;EAAnB,iGAAmB;EAAnB;AAAmB;AAAnB;EAAA,0CAAmB;EAAnB,uDAAmB;EAAnB;AAAmB;AAAnB;EAAA,gFAAmB;EAAnB,oGAAmB;EAAnB;AAAmB;AAAnB;EAAA,8BAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,6BAAmB;EAAnB,+QAAmB;EAAnB;AAAmB;AAAnB;EAAA,gKAAmB;EAAnB,wJAAmB;EAAnB,iLAAmB;EAAnB,wDAAmB;EAAnB;AAAmB;AAAnB;EAAA,wBAAmB;EAAnB,wDAAmB;EAAnB;AAAmB;AAAnB;EAAA,+FAAmB;EAAnB,wDAAmB;EAAnB;AAAmB;AAAnB;EAAA,4BAAmB;EAAnB,wDAAmB;EAAnB;AAAmB;AAAnB;EAAA,8BAAmB;EAAnB,wDAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;;AAFnB;EAAA;AA2JA;;AA3JA;EAAA;AA2JA;;AA3JA;EAAA;AA2JA;;AA3JA;EAAA;AA2JA;;AA3JA;EAAA;AA2JA;;AA3JA;EAAA,kBA2JA;EA3JA;AA2JA;;AA3JA;EAAA;AA2JA;;AA3JA;EAAA;AA2JA;;AA3JA;EAAA;AA2JA;;AA3JA;EAAA,kBA2JA;EA3JA;AA2JA;;AA3JA;EAAA,kBA2JA;EA3JA;AA2JA;;AA3JA;EAAA,kBA2JA;EA3JA;AA2JA;;AA3JA;EAAA,kBA2JA;EA3JA;AA2JA;;AA3JA;EAAA;AA2JA;;AA3JA;EAAA;AA2JA;;AA3JA;EAAA;AA2JA;;AA3JA;EAAA;AA2JA;;AA3JA;EAAA,kBA2JA;EA3JA;AA2JA;;AA3JA;EAAA;AA2JA;;AA3JA;EAAA;AA2JA;;AA3JA;EAAA;AA2JA;;AA3JA;EAAA;AA2JA;;AA3JA;EAAA;AA2JA;;AA3JA;EAAA,8EA2JA;EA3JA,wEA2JA;EA3JA;AA2JA;;AA3JA;EAAA;AA2JA;;AA3JA;EAAA;AA2JA;;AA3JA;EAAA,oBA2JA;EA3JA;AA2JA;;AA3JA;EAAA;AA2JA;;AA3JA;EAAA;AA2JA;;AA3JA;EAAA,oBA2JA;EA3JA;AA2JA;;AA3JA;EAAA,oBA2JA;EA3JA;AA2JA;;AA3JA;EAAA;AA2JA;;AA3JA;EAAA,oBA2JA;EA3JA;AA2JA;;AA3JA;EAAA,oBA2JA;EA3JA;AA2JA;;AA3JA;EAAA;AA2JA;;AA3JA;EAAA;AA2JA;;AA3JA;EAAA,+EA2JA;EA3JA,mGA2JA;EA3JA;AA2JA;;AA3JA;EAAA,6EA2JA;EA3JA,iGA2JA;EA3JA;AA2JA;;AA3JA;EAAA;AA2JA;;AA3JA;EAAA;AA2JA;;AA3JA;EAAA;AA2JA;;AA3JA;EAAA;AA2JA;;AA3JA;EAAA;AA2JA;;AA3JA;EAAA,8BA2JA;EA3JA;AA2JA;;AA3JA;EAAA,2GA2JA;EA3JA,yGA2JA;EA3JA;AA2JA;;AA3JA;EAAA,oBA2JA;EA3JA;AA2JA;;AA3JA;EAAA,oBA2JA;EA3JA;AA2JA;;AA3JA;EAAA;AA2JA;;AA3JA;EAAA;AA2JA;;AA3JA;EAAA;AA2JA;;AA3JA;EAAA;AA2JA;;AA3JA;EAAA,8BA2JA;EA3JA;AA2JA;;AA3JA;EAAA,2GA2JA;EA3JA,yGA2JA;EA3JA;AA2JA;;AA3JA;EAAA;AA2JA;;AA3JA;EAAA;AA2JA;;AA3JA;EAAA;AA2JA;;AA3JA;EAAA;AA2JA;;AA3JA;EAAA;AA2JA;;AA3JA;EAAA;AA2JA;;AA3JA;EAAA;AA2JA;;AA3JA;EAAA;AA2JA;;AA3JA;EAAA;AA2JA;;AA3JA;EAAA;AA2JA;;AA3JA;EAAA;AA2JA;;AA3JA;EAAA,yBA2JA;EA3JA;AA2JA;;AA3JA;EAAA,0BA2JA;EA3JA;AA2JA;;AA3JA;EAAA,yBA2JA;EA3JA;AA2JA;;AA3JA;EAAA,0BA2JA;EA3JA;AA2JA;;AA3JA;EAAA;AA2JA;;AA3JA;EAAA;AA2JA;;AA3JA;EAAA;AA2JA;;AA3JA;EAAA;AA2JA;;AA3JA;EAAA,sBA2JA;EA3JA;AA2JA;;AA3JA;EAAA,sBA2JA;EA3JA;AA2JA;;AA3JA;EAAA,sBA2JA;EA3JA;AA2JA;;AA3JA;EAAA;AA2JA;;AA3JA;EAAA,sBA2JA;EA3JA;AA2JA;;AA3JA;EAAA,sBA2JA;EA3JA;AA2JA;;AA3JA;EAAA,sBA2JA;EA3JA;AA2JA;;AA3JA;EAAA;AA2JA;;AA3JA;EAAA,kBA2JA;EA3JA;AA2JA;;AA3JA;EAAA,kBA2JA;EA3JA;AA2JA;;AA3JA;EAAA;AA2JA;;AA3JA;EAAA;AA2JA;;AA3JA;EAAA,kBA2JA;EA3JA;AA2JA;;AA3JA;EAAA;AA2JA;;AA3JA;EAAA;AA2JA;;AA3JA;EAAA;AA2JA;;AA3JA;EAAA;AA2JA;;AA3JA;EAAA;AA2JA;;AA3JA;EAAA;AA2JA;;AA3JA;EAAA;AA2JA;;AA3JA;EAAA;AA2JA;;AA3JA;EAAA,4DA2JA;EA3JA,kEA2JA;EA3JA;AA2JA;;AA3JA;EAAA,8EA2JA;EA3JA,wEA2JA;EA3JA;AA2JA;;AA3JA;EAAA,mEA2JA;EA3JA;AA2JA;;AA3JA;EAAA;AA2JA;;AA3JA;EAAA;AA2JA;;AA3JA;EAAA,oBA2JA;EA3JA;AA2JA;;AA3JA;EAAA,oBA2JA;EA3JA;AA2JA;;AA3JA;EAAA,oBA2JA;EA3JA;AA2JA;;AA3JA;EAAA,oBA2JA;EA3JA;AA2JA;;AA3JA;EAAA,oBA2JA;EA3JA;AA2JA;;AA3JA;EAAA,oBA2JA;EA3JA;AA2JA;;AA3JA;EAAA,oBA2JA;EA3JA;AA2JA;;AA3JA;EAAA,oBA2JA;EA3JA;AA2JA;;AA3JA;EAAA,2BA2JA;EA3JA;AA2JA;;AA3JA;EAAA,2BA2JA;EA3JA;AA2JA;;AA3JA;EAAA,kBA2JA;EA3JA;AA2JA;;AA3JA;EAAA,kBA2JA;EA3JA;AA2JA;;AA3JA;EAAA;AA2JA;;AA3JA;EAAA,oBA2JA;EA3JA;AA2JA;;AA3JA;EAAA,oBA2JA;EA3JA;AA2JA;;AA3JA;EAAA,gFA2JA;EA3JA,oGA2JA;EA3JA;AA2JA;;AA3JA;;EAAA;IAAA;EA2JA;;EA3JA;IAAA;EA2JA;;EA3JA;IAAA;EA2JA;;EA3JA;IAAA,uBA2JA;IA3JA,sDA2JA;IA3JA;EA2JA;;EA3JA;IAAA;EA2JA;AAAA;;AA3JA;;EAAA;IAAA;EA2JA;AAAA;;AA3JA;EAAA,gBA2JA;EA3JA,oBA2JA;EA3JA,4BA2JA;EA3JA;AA2JA\",\"sourcesContent\":[\"@tailwind base;\\n@tailwind components;\\n@tailwind utilities;\\n\\n@layer base {\\n  :root {\\n    /* Vectal.ai Light Mode Colors */\\n    --background: 244 244 246;       /* #F3F4F6 - neutral gray background */\\n    --foreground: 31 41 55;          /* #1F2937 - dark gray text */\\n    --card: 255 255 255;             /* #FFFFFF - white cards */\\n    --card-foreground: 31 41 55;     /* #1F2937 - dark gray card text */\\n    --popover: 255 255 255;          /* #FFFFFF - white popover */\\n    --popover-foreground: 31 41 55;  /* #1F2937 - dark gray popover text */\\n    --primary: 59 130 246;           /* #3B82F6 - calming blue primary */\\n    --primary-foreground: 255 255 255; /* #FFFFFF - white text on primary */\\n    --secondary: 229 231 235;        /* #E5E7EB - light gray secondary */\\n    --secondary-foreground: 31 41 55; /* #1F2937 - dark gray secondary text */\\n    --muted: 249 250 251;            /* #F9FAFB - very light gray muted */\\n    --muted-foreground: 107 114 128; /* #6B7280 - medium gray muted text */\\n    --accent: 167 243 208;           /* #A7F3D0 - sage green accent */\\n    --accent-foreground: 31 41 55;   /* #1F2937 - dark gray accent text */\\n    --destructive: 239 68 68;        /* #EF4444 - red for destructive */\\n    --destructive-foreground: 255 255 255; /* #FFFFFF - white text on destructive */\\n    --border: 229 231 235;           /* #E5E7EB - light gray border */\\n    --input: 249 250 251;            /* #F9FAFB - very light gray input */\\n    --ring: 59 130 246;              /* #3B82F6 - blue focus ring */\\n    --radius: 0.5rem;\\n    --success: 34 197 94;            /* #22C55E - green for success */\\n    --warning: 245 158 11;           /* #F59E0B - amber for warning */\\n  }\\n\\n  .dark {\\n    /* Vectal.ai Dark Mode Colors */\\n    --background: 31 41 55;          /* #1F2937 - dark gray background */\\n    --foreground: 243 244 246;       /* #F3F4F6 - light gray text */\\n    --card: 55 65 81;                /* #374151 - darker gray cards */\\n    --card-foreground: 243 244 246;  /* #F3F4F6 - light gray card text */\\n    --popover: 55 65 81;             /* #374151 - darker gray popover */\\n    --popover-foreground: 243 244 246; /* #F3F4F6 - light gray popover text */\\n    --primary: 96 165 250;           /* #60A5FA - lighter blue primary for dark mode */\\n    --primary-foreground: 31 41 55;  /* #1F2937 - dark gray text on primary */\\n    --secondary: 75 85 99;           /* #4B5563 - medium gray secondary */\\n    --secondary-foreground: 243 244 246; /* #F3F4F6 - light gray secondary text */\\n    --muted: 75 85 99;               /* #4B5563 - medium gray muted */\\n    --muted-foreground: 156 163 175; /* #9CA3AF - lighter gray muted text */\\n    --accent: 167 243 208;           /* #A7F3D0 - sage green accent (same in both modes) */\\n    --accent-foreground: 31 41 55;   /* #1F2937 - dark gray accent text */\\n    --destructive: 239 68 68;        /* #EF4444 - red for destructive */\\n    --destructive-foreground: 243 244 246; /* #F3F4F6 - light gray text on destructive */\\n    --border: 75 85 99;              /* #4B5563 - medium gray border */\\n    --input: 75 85 99;               /* #4B5563 - medium gray input */\\n    --ring: 96 165 250;              /* #60A5FA - lighter blue focus ring */\\n    --success: 34 197 94;            /* #22C55E - green for success */\\n    --warning: 245 158 11;           /* #F59E0B - amber for warning */\\n  }\\n}\\n\\n@layer base {\\n  * {\\n    @apply border-border;\\n  }\\n  body {\\n    @apply bg-background text-foreground;\\n    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue', sans-serif;\\n  }\\n}\\n\\n@layer components {\\n  /* Vectal.ai Design System Components */\\n  .vectal-card {\\n    @apply bg-card border border-border rounded-xl p-6 shadow-sm hover:shadow-md transition-all duration-300;\\n  }\\n\\n  .vectal-button-primary {\\n    @apply bg-primary text-primary-foreground hover:bg-primary/90 active:bg-primary/95 px-4 py-2.5 rounded-lg font-medium transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed;\\n  }\\n\\n  .vectal-button-secondary {\\n    @apply bg-secondary text-secondary-foreground hover:bg-secondary/80 active:bg-secondary/90 px-4 py-2.5 rounded-lg font-medium transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed;\\n  }\\n\\n  .vectal-button-ghost {\\n    @apply text-foreground hover:bg-muted active:bg-muted/80 px-4 py-2.5 rounded-lg font-medium transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2;\\n  }\\n\\n  .vectal-input {\\n    @apply bg-input border border-border rounded-lg px-3 py-2.5 text-foreground placeholder:text-muted-foreground focus:outline-none focus:ring-2 focus:ring-ring focus:border-primary transition-all duration-200;\\n  }\\n\\n  .vectal-textarea {\\n    @apply bg-input border border-border rounded-lg px-3 py-2.5 text-foreground placeholder:text-muted-foreground focus:outline-none focus:ring-2 focus:ring-ring focus:border-primary transition-all duration-200 resize-none min-h-[100px];\\n  }\\n\\n  .vectal-sidebar {\\n    @apply bg-card border-r border-border backdrop-blur-sm;\\n  }\\n\\n  .vectal-task-card {\\n    @apply bg-card border border-border rounded-xl p-4 shadow-sm hover:shadow-md hover:border-primary/20 transition-all duration-300 cursor-pointer;\\n  }\\n\\n  .vectal-modal-overlay {\\n    @apply fixed inset-0 bg-background/80 backdrop-blur-sm z-50 flex items-center justify-center p-4;\\n  }\\n\\n  .vectal-modal {\\n    @apply bg-card border border-border rounded-xl shadow-xl max-w-md w-full max-h-[90vh] overflow-y-auto;\\n  }\\n\\n  .vectal-badge {\\n    @apply inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium;\\n  }\\n\\n  .vectal-badge-priority-high {\\n    @apply bg-destructive/10 text-destructive border border-destructive/20;\\n  }\\n\\n  .vectal-badge-priority-medium {\\n    @apply bg-warning/10 text-warning border border-warning/20;\\n  }\\n\\n  .vectal-badge-priority-low {\\n    @apply bg-success/10 text-success border border-success/20;\\n  }\\n\\n  .vectal-badge-status {\\n    @apply bg-accent/10 text-accent-foreground border border-accent/20;\\n  }\\n\\n  .vectal-divider {\\n    @apply border-t border-border my-4;\\n  }\\n\\n  .vectal-scrollbar {\\n    scrollbar-width: thin;\\n    scrollbar-color: rgb(var(--muted)) transparent;\\n  }\\n\\n  .vectal-scrollbar::-webkit-scrollbar {\\n    width: 6px;\\n  }\\n\\n  .vectal-scrollbar::-webkit-scrollbar-track {\\n    background: transparent;\\n  }\\n\\n  .vectal-scrollbar::-webkit-scrollbar-thumb {\\n    background-color: rgb(var(--muted));\\n    border-radius: 3px;\\n  }\\n\\n  .vectal-scrollbar::-webkit-scrollbar-thumb:hover {\\n    background-color: rgb(var(--muted-foreground));\\n  }\\n}\\n\"],\"sourceRoot\":\"\"}]);\n// Exports\n/* harmony default export */ __webpack_exports__[\"default\"] = (___CSS_LOADER_EXPORT___);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[7].oneOf[13].use[1]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[7].oneOf[13].use[2]!./styles/globals.css\n"));

/***/ })

});