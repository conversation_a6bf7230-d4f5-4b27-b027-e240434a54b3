import { create } from 'zustand';
import { createTask, updateTask, deleteTask as deleteTaskFromDB, getUserTasks } from './firebase-utils';

export const useTaskStore = create((set, get) => ({
  tasks: [],
  user: null,
  loading: false,
  error: null,

  setUser: (user) => set({ user }),
  
  setLoading: (loading) => set({ loading }),
  
  setError: (error) => set({ error }),

  loadTasks: async (userId) => {
    set({ loading: true, error: null });
    try {
      const tasks = await getUserTasks(userId);
      set({ tasks, loading: false });
    } catch (error) {
      set({ error: error.message, loading: false });
    }
  },

  addTask: async (taskData) => {
    const { user } = get();
    if (!user) return;
    
    set({ loading: true, error: null });
    try {
      const newTask = await createTask(user.uid, taskData);
      set((state) => ({
        tasks: [newTask, ...state.tasks],
        loading: false
      }));
      return newTask;
    } catch (error) {
      set({ error: error.message, loading: false });
      throw error;
    }
  },

  updateTask: async (taskId, updates) => {
    const { user } = get();
    if (!user) return;

    set({ loading: true, error: null });
    try {
      await updateTask(taskId, updates);
      set((state) => ({
        tasks: state.tasks.map(task =>
          task.id === taskId ? { ...task, ...updates } : task
        ),
        loading: false
      }));
    } catch (error) {
      console.error('Error updating task:', error);
      set({ error: error.message, loading: false });
      throw error;
    }
  },

  deleteTask: async (taskId) => {
    const { user } = get();
    if (!user) return;

    set({ loading: true, error: null });
    try {
      await deleteTaskFromDB(taskId);
      set((state) => ({
        tasks: state.tasks.filter(task => task.id !== taskId),
        loading: false
      }));
    } catch (error) {
      console.error('Error deleting task:', error);
      set({ error: error.message, loading: false });
      throw error;
    }
  },

  moveTask: async (taskId, newColumn) => {
    const { updateTask } = get();
    await updateTask(taskId, { status: newColumn });
  },

  getTasksByStatus: (status) => {
    const { tasks } = get();
    return tasks.filter(task => task.status === status);
  },

  getTaskCount: () => {
    const { tasks } = get();
    return tasks.length;
  },

  findTaskByName: (name) => {
    const { tasks } = get();
    return tasks.find(task => 
      task.title.toLowerCase().includes(name.toLowerCase())
    );
  }
}));
