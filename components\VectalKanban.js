import { useState } from 'react';
import {
  DndContext,
  DragOverlay,
  closestCorners,
  KeyboardSensor,
  PointerSensor,
  useSensor,
  useSensors,
} from '@dnd-kit/core';
import {
  SortableContext,
  sortableKeyboardCoordinates,
  verticalListSortingStrategy,
} from '@dnd-kit/sortable';
import { useTaskStore } from '../lib/store';
import VectalKanbanColumn from './VectalKanbanColumn';
import VectalTaskCard from './VectalTaskCard';
import { Plus } from 'lucide-react';

const columns = [
  {
    id: 'todo',
    title: 'To Do',
    color: 'border-l-gray-400',
    bgColor: 'bg-gray-50/50 dark:bg-gray-800/30'
  },
  {
    id: 'inprogress',
    title: 'In Progress',
    color: 'border-l-primary',
    bgColor: 'bg-primary/5'
  },
  {
    id: 'done',
    title: 'Done',
    color: 'border-l-green-500',
    bgColor: 'bg-green-50/50 dark:bg-green-900/20'
  }
];

export default function VectalKanban({ searchQuery = '' }) {
  const { tasks, moveTask, addTask } = useTaskStore();
  const [activeId, setActiveId] = useState(null);
  const [newTaskTitles, setNewTaskTitles] = useState({
    todo: '',
    inprogress: '',
    done: ''
  });
  const [showAddTask, setShowAddTask] = useState({
    todo: false,
    inprogress: false,
    done: false
  });

  const sensors = useSensors(
    useSensor(PointerSensor),
    useSensor(KeyboardSensor, {
      coordinateGetter: sortableKeyboardCoordinates,
    })
  );

  const getTasksByStatus = (status) => {
    let filteredTasks = tasks.filter(task => task.status === status);
    
    if (searchQuery) {
      filteredTasks = filteredTasks.filter(task =>
        task.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
        (task.description && task.description.toLowerCase().includes(searchQuery.toLowerCase()))
      );
    }
    
    return filteredTasks;
  };

  const handleDragStart = (event) => {
    setActiveId(event.active.id);
  };

  const handleDragEnd = async (event) => {
    const { active, over } = event;
    
    if (!over) {
      setActiveId(null);
      return;
    }

    const activeId = active.id;
    const overId = over.id;

    const activeTask = tasks.find(task => task.id === activeId);
    if (!activeTask) {
      setActiveId(null);
      return;
    }

    let newStatus = activeTask.status;

    // Check if dropped on a column
    if (columns.some(col => col.id === overId)) {
      newStatus = overId;
    } else {
      // Check if dropped on another task
      const overTask = tasks.find(task => task.id === overId);
      if (overTask) {
        newStatus = overTask.status;
      }
    }

    if (newStatus !== activeTask.status) {
      try {
        await moveTask(activeId, newStatus);
      } catch (error) {
        console.error('Failed to move task:', error);
      }
    }

    setActiveId(null);
  };

  const handleAddTask = async (columnId) => {
    const title = newTaskTitles[columnId].trim();
    if (!title) return;

    try {
      await addTask({
        title,
        description: '',
        status: columnId,
        priority: 'medium'
      });
      
      setNewTaskTitles(prev => ({ ...prev, [columnId]: '' }));
      setShowAddTask(prev => ({ ...prev, [columnId]: false }));
    } catch (error) {
      console.error('Failed to add task:', error);
    }
  };

  const handleKeyPress = (e, columnId) => {
    if (e.key === 'Enter') {
      handleAddTask(columnId);
    } else if (e.key === 'Escape') {
      setShowAddTask(prev => ({ ...prev, [columnId]: false }));
      setNewTaskTitles(prev => ({ ...prev, [columnId]: '' }));
    }
  };

  const activeTask = activeId ? tasks.find(task => task.id === activeId) : null;

  return (
    <div className="h-full p-6">
      <DndContext
        sensors={sensors}
        collisionDetection={closestCorners}
        onDragStart={handleDragStart}
        onDragEnd={handleDragEnd}
      >
        <div className="grid grid-cols-3 gap-6 h-full">
          {columns.map((column) => (
            <SortableContext
              key={column.id}
              items={getTasksByStatus(column.id).map(task => task.id)}
              strategy={verticalListSortingStrategy}
            >
              <VectalKanbanColumn
                id={column.id}
                title={column.title}
                color={column.color}
                bgColor={column.bgColor}
                tasks={getTasksByStatus(column.id)}
                onAddTask={() => setShowAddTask(prev => ({ ...prev, [column.id]: true }))}
                showAddTask={showAddTask[column.id]}
                newTaskTitle={newTaskTitles[column.id]}
                onNewTaskTitleChange={(value) => 
                  setNewTaskTitles(prev => ({ ...prev, [column.id]: value }))
                }
                onSubmitTask={() => handleAddTask(column.id)}
                onCancelTask={() => {
                  setShowAddTask(prev => ({ ...prev, [column.id]: false }));
                  setNewTaskTitles(prev => ({ ...prev, [column.id]: '' }));
                }}
                onKeyPress={(e) => handleKeyPress(e, column.id)}
              />
            </SortableContext>
          ))}
        </div>

        <DragOverlay>
          {activeTask ? <VectalTaskCard task={activeTask} isDragging /> : null}
        </DragOverlay>
      </DndContext>
    </div>
  );
}
