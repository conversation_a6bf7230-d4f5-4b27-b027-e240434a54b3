import { useState } from 'react';
import { useTaskStore } from '../lib/store';
import { 
  Calendar,
  CheckSquare,
  Grid3X3,
  List,
  MessageSquare,
  Plus,
  Settings,
  ChevronDown,
  Folder,
  Hash
} from 'lucide-react';

export default function VectalSidebar({ activeView, onViewChange, onNewTask, onToggleAI }) {
  const { tasks } = useTaskStore();
  const [expandedSections, setExpandedSections] = useState({
    projects: true,
    views: true
  });

  const toggleSection = (section) => {
    setExpandedSections(prev => ({
      ...prev,
      [section]: !prev[section]
    }));
  };

  const todoCount = tasks.filter(t => t.status === 'todo').length;
  const inProgressCount = tasks.filter(t => t.status === 'inprogress').length;
  const doneCount = tasks.filter(t => t.status === 'done').length;

  const sidebarItems = [
    {
      section: 'views',
      title: 'Views',
      items: [
        {
          id: 'today',
          label: 'Today',
          icon: Calendar,
          count: todoCount + inProgressCount,
          active: false
        },
        {
          id: 'upcoming',
          label: 'Upcoming',
          icon: CheckSquare,
          count: 0,
          active: false
        },
        {
          id: 'kanban',
          label: 'Kanban',
          icon: Grid3X3,
          count: tasks.length,
          active: activeView === 'kanban'
        },
        {
          id: 'list',
          label: 'List',
          icon: List,
          count: tasks.length,
          active: activeView === 'list'
        }
      ]
    },
    {
      section: 'projects',
      title: 'Projects',
      items: [
        {
          id: 'personal',
          label: 'Personal',
          icon: Hash,
          count: tasks.length,
          active: false,
          color: 'text-blue-400'
        }
      ]
    }
  ];

  return (
    <div className="w-64 vectal-sidebar h-full flex flex-col">
      {/* Header */}
      <div className="p-4 border-b border-border">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-2">
            <div className="w-8 h-8 bg-primary rounded-lg flex items-center justify-center">
              <CheckSquare className="h-4 w-4 text-primary-foreground" />
            </div>
            <span className="font-semibold text-foreground">vectal.ai</span>
          </div>
          <button
            onClick={onNewTask}
            className="p-1.5 hover:bg-muted rounded-lg transition-colors"
          >
            <Plus className="h-4 w-4 text-muted-foreground" />
          </button>
        </div>
      </div>

      {/* Navigation */}
      <div className="flex-1 overflow-y-auto p-2">
        {sidebarItems.map((section) => (
          <div key={section.section} className="mb-6">
            <button
              onClick={() => toggleSection(section.section)}
              className="flex items-center justify-between w-full px-2 py-1 text-xs font-medium text-muted-foreground hover:text-foreground transition-colors"
            >
              <span className="uppercase tracking-wider">{section.title}</span>
              <ChevronDown 
                className={`h-3 w-3 transition-transform ${
                  expandedSections[section.section] ? 'rotate-0' : '-rotate-90'
                }`} 
              />
            </button>
            
            {expandedSections[section.section] && (
              <div className="mt-2 space-y-1">
                {section.items.map((item) => (
                  <button
                    key={item.id}
                    onClick={() => onViewChange(item.id)}
                    className={`w-full flex items-center justify-between px-2 py-2 rounded-lg text-sm transition-all ${
                      item.active
                        ? 'bg-primary/10 text-primary border border-primary/20'
                        : 'text-muted-foreground hover:text-foreground hover:bg-muted'
                    }`}
                  >
                    <div className="flex items-center space-x-3">
                      <item.icon className={`h-4 w-4 ${item.color || ''}`} />
                      <span>{item.label}</span>
                    </div>
                    {item.count > 0 && (
                      <span className={`text-xs px-1.5 py-0.5 rounded ${
                        item.active 
                          ? 'bg-primary/20 text-primary' 
                          : 'bg-muted text-muted-foreground'
                      }`}>
                        {item.count}
                      </span>
                    )}
                  </button>
                ))}
              </div>
            )}
          </div>
        ))}
      </div>

      {/* Bottom Actions */}
      <div className="p-4 border-t border-border">
        <button
          onClick={onToggleAI}
          className="w-full flex items-center space-x-3 px-3 py-2 text-sm text-muted-foreground hover:text-foreground hover:bg-muted rounded-lg transition-all"
        >
          <MessageSquare className="h-4 w-4" />
          <span>AI Assistant</span>
        </button>
        <button className="w-full flex items-center space-x-3 px-3 py-2 text-sm text-muted-foreground hover:text-foreground hover:bg-muted rounded-lg transition-all mt-1">
          <Settings className="h-4 w-4" />
          <span>Settings</span>
        </button>
      </div>
    </div>
  );
}
