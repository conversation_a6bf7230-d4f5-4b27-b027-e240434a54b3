import { useState, useRef, useEffect } from 'react';
import { useTaskStore } from '../lib/store';
import { parseTaskCommand } from '../lib/openrouter-config';
import { Button } from './ui/button';
import { Input } from './ui/input';
import { Badge } from './ui/badge';
import { Send, X, Bot, User, Minimize2, Maximize2, MessageSquare, Loader2 } from 'lucide-react';
import { cn } from '../lib/utils';
export default function VectalAIChat({ onClose }) {
  const [messages, setMessages] = useState([
    {
      id: 1,
      type: 'assistant',
      content: 'Hi! I\'m your AI task assistant. I can help you create, move, and manage your tasks. Try saying "Create a new task called Design Homepage" or "Move task X to Done".',
      timestamp: new Date()
    }
  ]);
  const [input, setInput] = useState('');
  const [loading, setLoading] = useState(false);
  const [isMinimized, setIsMinimized] = useState(false);
  const messagesEndRef = useRef(null);
  const { tasks, addTask, updateTask, deleteTask, findTaskByName } = useTaskStore();
  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  };
  useEffect(() => {
    scrollToBottom();
  }, [messages]);
  const getColumnDisplayName = (column) => {
    const columnMap = {
      'todo': 'To Do',
      'inprogress': 'In Progress',
      'done': 'Done'
    };
    return columnMap[column] || column;
  };
  const handleTaskCommand = async (command) => {
    try {
      switch (command.action) {
        case 'create':
          await addTask({
            title: command.taskName,
            description: '',
            status: command.column || 'todo',
            priority: 'medium'
          });
          return `✅ Task "${command.taskName}" created successfully in the ${getColumnDisplayName(command.column || 'todo')} column.`;
        case 'move':
          const taskToMove = findTaskByName(command.taskName);
          if (!taskToMove) {
            return `❌ I couldn't find a task with the name "${command.taskName}". Please check the task name and try again.`;
          }
          await updateTask(taskToMove.id, { status: command.column });
          return `✅ Task "${taskToMove.title}" moved to ${getColumnDisplayName(command.column)} successfully.`;
        case 'delete':
          const taskToDelete = findTaskByName(command.taskName);
          if (!taskToDelete) {
            return `❌ I couldn't find a task with the name "${command.taskName}". Please check the task name and try again.`;
          }
          await deleteTask(taskToDelete.id);
          return `✅ Task "${taskToDelete.title}" deleted successfully.`;
        default:
          return await handleGeneralQuery(command.message);
      }
    } catch (error) {
      console.error('Error executing task command:', error);
      return 'Sorry, I encountered an error while processing your request. Please try again.';
    }
  };
  const handleGeneralQuery = async (message) => {
    try {
      const taskCount = tasks.length;
      const todoCount = tasks.filter(t => t.status === 'todo').length;
      const inProgressCount = tasks.filter(t => t.status === 'inprogress').length;
      const doneCount = tasks.filter(t => t.status === 'done').length;
      const response = await fetch('/api/ai-assistant', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          message,
          context: {
            taskCount,
            todoCount,
            inProgressCount,
            doneCount,
            tasks: tasks.map(t => ({ title: t.title, status: t.status, priority: t.priority }))
          }
        })
      });
      if (!response.ok) {
        throw new Error('Failed to get AI response');
      }
      const data = await response.json();
      return data.response;
    } catch (error) {
      console.error('Error calling AI API:', error);
      const lowerMessage = message.toLowerCase();
      const taskCount = tasks.length;
      const todoCount = tasks.filter(t => t.status === 'todo').length;
      const inProgressCount = tasks.filter(t => t.status === 'inprogress').length;
      const doneCount = tasks.filter(t => t.status === 'done').length;
      if (lowerMessage.includes('how many') || lowerMessage.includes('count')) {
        return `📊 You currently have ${taskCount} total tasks: ${todoCount} in To Do, ${inProgressCount} in Progress, and ${doneCount} completed.`;
      }
      if (lowerMessage.includes('help') || lowerMessage.includes('what can you do')) {
        return '🤖 I can help you with:\n• Creating tasks: "Create a new task called [name]"\n• Moving tasks: "Move task [name] to [To Do/In Progress/Done]"\n• Deleting tasks: "Delete task [name]"\n• Getting task summaries and priorities\n• Answering questions about your tasks';
      }
      return '🤖 I\'m here to help with your tasks! You can ask me to create, move, or delete tasks, or ask about your current task status.';
    }
  };
  const handleSubmit = async (e) => {
    e.preventDefault();
    if (!input.trim() || loading) return;
    const userMessage = {
      id: Date.now(),
      type: 'user',
      content: input.trim(),
      timestamp: new Date()
    };
    setMessages(prev => [...prev, userMessage]);
    setInput('');
    setLoading(true);
    try {
      const command = parseTaskCommand(input.trim());
      const response = await handleTaskCommand(command);
      const assistantMessage = {
        id: Date.now() + 1,
        type: 'assistant',
        content: response,
        timestamp: new Date()
      };
      setMessages(prev => [...prev, assistantMessage]);
    } catch (error) {
      console.error('Error processing message:', error);
      const errorMessage = {
        id: Date.now() + 1,
        type: 'assistant',
        content: 'Sorry, I encountered an error. Please try again.',
        timestamp: new Date()
      };
      setMessages(prev => [...prev, errorMessage]);
    } finally {
      setLoading(false);
    }
  };
  const formatTime = (timestamp) => {
    return new Date(timestamp).toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
  };
  return (
    <div className={cn(
      "fixed right-6 bottom-6 z-50 bg-card border border-border rounded-xl shadow-xl transition-all duration-300",
      isMinimized ? "w-80 h-16" : "w-96 h-[500px]"
    )}>
      <div className="flex items-center justify-between p-4 border-b border-border">
        <div className="flex items-center gap-2">
          <div className="w-8 h-8 bg-primary rounded-lg flex items-center justify-center">
            <MessageSquare className="h-4 w-4 text-primary-foreground" />
          </div>
          <div>
            <h3 className="font-semibold text-foreground">AI Assistant</h3>
            {!isMinimized && (
              <p className="text-xs text-muted-foreground">Ask me anything about your tasks</p>
            )}
          </div>
        </div>
        <div className="flex items-center gap-1">
          <Button
            variant="ghost"
            size="icon"
            onClick={() => setIsMinimized(!isMinimized)}
            className="h-8 w-8"
          >
            {isMinimized ? <Maximize2 className="h-4 w-4" /> : <Minimize2 className="h-4 w-4" />}
          </Button>
          <Button
            variant="ghost"
            size="icon"
            onClick={onClose}
            className="h-8 w-8"
          >
            <X className="h-4 w-4" />
          </Button>
        </div>
      </div>
      {!isMinimized && (
        <>
          <div className="flex-1 p-4 space-y-4 h-80 overflow-y-auto vectal-scrollbar">
            {messages.map((message) => (
              <div
                key={message.id}
                className={cn(
                  "flex gap-3",
                  message.type === 'user' ? 'justify-end' : 'justify-start'
                )}
              >
                {message.type === 'assistant' && (
                  <div className="w-8 h-8 bg-primary rounded-full flex items-center justify-center flex-shrink-0">
                    <Bot className="h-4 w-4 text-primary-foreground" />
                  </div>
                )}
                <div
                  className={cn(
                    "max-w-[80%] rounded-lg px-3 py-2 text-sm",
                    message.type === 'user'
                      ? 'bg-primary text-primary-foreground'
                      : 'bg-muted text-foreground'
                  )}
                >
                  <div className="whitespace-pre-wrap">{message.content}</div>
                  <div className={cn(
                    "text-xs mt-1 opacity-70",
                    message.type === 'user' ? 'text-primary-foreground' : 'text-muted-foreground'
                  )}>
                    {formatTime(message.timestamp)}
                  </div>
                </div>
                {message.type === 'user' && (
                  <div className="w-8 h-8 bg-secondary rounded-full flex items-center justify-center flex-shrink-0">
                    <User className="h-4 w-4 text-secondary-foreground" />
                  </div>
                )}
              </div>
            ))}
            {loading && (
              <div className="flex gap-3 justify-start">
                <div className="w-8 h-8 bg-primary rounded-full flex items-center justify-center flex-shrink-0">
                  <Bot className="h-4 w-4 text-primary-foreground" />
                </div>
                <div className="bg-muted text-foreground rounded-lg px-3 py-2 text-sm flex items-center gap-2">
                  <Loader2 className="h-4 w-4 animate-spin" />
                  Thinking...
                </div>
              </div>
            )}
            <div ref={messagesEndRef} />
          </div>
          <div className="p-4 border-t border-border">
            <form onSubmit={handleSubmit} className="flex gap-2">
              <Input
                value={input}
                onChange={(e) => setInput(e.target.value)}
                placeholder="Ask me about your tasks..."
                disabled={loading}
                className="flex-1"
              />
              <Button type="submit" disabled={loading || !input.trim()} size="icon">
                <Send className="h-4 w-4" />
              </Button>
            </form>
            <div className="flex flex-wrap gap-1 mt-2">
              <Badge variant="outline" className="text-xs cursor-pointer hover:bg-muted" onClick={() => setInput('Create a new task called ')}>
                Create task
              </Badge>
              <Badge variant="outline" className="text-xs cursor-pointer hover:bg-muted" onClick={() => setInput('How many tasks do I have?')}>
                Task count
              </Badge>
              <Badge variant="outline" className="text-xs cursor-pointer hover:bg-muted" onClick={() => setInput('Help')}>
                Help
              </Badge>
            </div>
          </div>
        </>
      )}
    </div>
  );
}
