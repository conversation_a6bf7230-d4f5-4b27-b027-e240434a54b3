import { useState } from 'react';
import { useAuth } from '../contexts/AuthContext';
import { useTaskStore } from '../lib/store';
import { 
  Plus, 
  Search, 
  Filter, 
  MoreHorizontal, 
  Calendar,
  List,
  Grid3X3,
  Settings,
  Bell,
  User,
  LogOut,
  MessageSquare
} from 'lucide-react';
import VectalSidebar from './VectalSidebar';
import VectalKanban from './VectalKanban';
import VectalTaskModal from './VectalTaskModal';
import VectalAIChat from './VectalAIChat';
import ThemeToggle from './ThemeToggle';

export default function VectalDashboard() {
  const { user, logout } = useAuth();
  const { tasks } = useTaskStore();
  const [activeView, setActiveView] = useState('kanban');
  const [showTaskModal, setShowTaskModal] = useState(false);
  const [showAIChat, setShowAIChat] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');

  const handleLogout = async () => {
    try {
      await logout();
    } catch (error) {
      console.error('Failed to log out:', error);
    }
  };

  return (
    <div className="h-screen bg-background flex">
      {/* Left Sidebar */}
      <VectalSidebar 
        activeView={activeView}
        onViewChange={setActiveView}
        onNewTask={() => setShowTaskModal(true)}
        onToggleAI={() => setShowAIChat(!showAIChat)}
      />

      {/* Main Content */}
      <div className="flex-1 flex flex-col">
        {/* Top Header */}
        <header className="bg-card border-b border-border px-6 py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <h1 className="text-xl font-semibold text-foreground">
                {activeView === 'kanban' ? 'Kanban Board' : 'Task List'}
              </h1>
              <div className="flex items-center space-x-2">
                <span className="text-sm text-muted-foreground">
                  {tasks.length} tasks
                </span>
              </div>
            </div>

            <div className="flex items-center space-x-4">
              {/* Search */}
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                <input
                  type="text"
                  placeholder="Search tasks..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className="vectal-input pl-10 w-64"
                />
              </div>

              {/* View Toggle */}
              <div className="flex items-center bg-muted rounded-lg p-1">
                <button
                  onClick={() => setActiveView('kanban')}
                  className={`p-2 rounded-md transition-all ${
                    activeView === 'kanban'
                      ? 'bg-background shadow-sm text-foreground'
                      : 'text-muted-foreground hover:text-foreground'
                  }`}
                >
                  <Grid3X3 className="h-4 w-4" />
                </button>
                <button
                  onClick={() => setActiveView('list')}
                  className={`p-2 rounded-md transition-all ${
                    activeView === 'list'
                      ? 'bg-background shadow-sm text-foreground'
                      : 'text-muted-foreground hover:text-foreground'
                  }`}
                >
                  <List className="h-4 w-4" />
                </button>
              </div>

              {/* Add Task Button */}
              <button
                onClick={() => setShowTaskModal(true)}
                className="vectal-button-primary flex items-center space-x-2"
              >
                <Plus className="h-4 w-4" />
                <span>Add Task</span>
              </button>

              {/* User Menu */}
              <div className="flex items-center space-x-2">
                <ThemeToggle />
                <button className="p-2 hover:bg-muted rounded-lg transition-colors">
                  <Bell className="h-4 w-4 text-muted-foreground" />
                </button>
                <div className="flex items-center space-x-2 pl-2">
                  <div className="w-8 h-8 bg-primary rounded-full flex items-center justify-center">
                    <User className="h-4 w-4 text-primary-foreground" />
                  </div>
                  <span className="text-sm font-medium text-foreground">
                    {user?.displayName || 'User'}
                  </span>
                  <button
                    onClick={handleLogout}
                    className="p-1 hover:bg-muted rounded transition-colors"
                  >
                    <LogOut className="h-4 w-4 text-muted-foreground" />
                  </button>
                </div>
              </div>
            </div>
          </div>
        </header>

        {/* Main Content Area */}
        <main className="flex-1 overflow-hidden">
          {activeView === 'kanban' ? (
            <VectalKanban searchQuery={searchQuery} />
          ) : (
            <div className="p-6">
              <div className="text-center py-20">
                <List className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                <h3 className="text-lg font-medium text-foreground mb-2">List View</h3>
                <p className="text-muted-foreground">Coming soon...</p>
              </div>
            </div>
          )}
        </main>
      </div>

      {/* AI Chat */}
      {showAIChat && (
        <VectalAIChat onClose={() => setShowAIChat(false)} />
      )}

      {/* Task Modal */}
      {showTaskModal && (
        <VectalTaskModal
          isOpen={showTaskModal}
          onClose={() => setShowTaskModal(false)}
        />
      )}
    </div>
  );
}
