import { useDroppable } from '@dnd-kit/core';
import { SortableContext, verticalListSortingStrategy } from '@dnd-kit/sortable';
import { Plus, X, Check } from 'lucide-react';
import VectalTaskCard from './VectalTaskCard';

export default function VectalKanbanColumn({ 
  id, 
  title, 
  color, 
  bgColor, 
  tasks, 
  onAddTask,
  showAddTask,
  newTaskTitle,
  onNewTaskTitleChange,
  onSubmitTask,
  onCancelTask,
  onKeyPress
}) {
  const { setNodeRef } = useDroppable({ id });

  return (
    <div className={`flex flex-col h-full rounded-lg border-l-4 ${color} ${bgColor} p-4`}>
      {/* Column Header */}
      <div className="flex items-center justify-between mb-4">
        <div className="flex items-center space-x-2">
          <h3 className="font-semibold text-foreground">{title}</h3>
          <span className="bg-muted text-muted-foreground text-xs px-2 py-1 rounded-full font-medium">
            {tasks.length}
          </span>
        </div>
        <button
          onClick={onAddTask}
          className="p-1 hover:bg-muted rounded-lg transition-colors opacity-0 group-hover:opacity-100"
        >
          <Plus className="h-4 w-4 text-muted-foreground" />
        </button>
      </div>

      {/* Tasks Container */}
      <div
        ref={setNodeRef}
        className="flex-1 space-y-3 min-h-[200px] group"
      >
        <SortableContext items={tasks.map(task => task.id)} strategy={verticalListSortingStrategy}>
          {tasks.map((task) => (
            <VectalTaskCard key={task.id} task={task} />
          ))}
        </SortableContext>

        {/* Add Task Input */}
        {showAddTask && (
          <div className="vectal-task-card border-dashed border-2 border-primary/30">
            <div className="flex items-center space-x-2">
              <input
                type="text"
                value={newTaskTitle}
                onChange={(e) => onNewTaskTitleChange(e.target.value)}
                onKeyDown={onKeyPress}
                placeholder="Enter task title..."
                className="flex-1 bg-transparent border-none outline-none text-sm text-foreground placeholder:text-muted-foreground"
                autoFocus
              />
              <div className="flex items-center space-x-1">
                <button
                  onClick={onSubmitTask}
                  disabled={!newTaskTitle.trim()}
                  className="p-1 hover:bg-primary/10 rounded text-primary disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  <Check className="h-3 w-3" />
                </button>
                <button
                  onClick={onCancelTask}
                  className="p-1 hover:bg-muted rounded text-muted-foreground"
                >
                  <X className="h-3 w-3" />
                </button>
              </div>
            </div>
          </div>
        )}

        {/* Empty State */}
        {tasks.length === 0 && !showAddTask && (
          <div className="flex flex-col items-center justify-center h-32 text-muted-foreground">
            <div className="w-12 h-12 bg-muted rounded-lg flex items-center justify-center mb-3">
              <Plus className="h-6 w-6" />
            </div>
            <p className="text-sm">No tasks yet</p>
            <button
              onClick={onAddTask}
              className="text-xs text-primary hover:underline mt-1"
            >
              Add your first task
            </button>
          </div>
        )}

        {/* Drop Zone Indicator */}
        <div className="opacity-0 group-hover:opacity-100 transition-opacity">
          <div className="border-2 border-dashed border-muted rounded-lg p-4 text-center">
            <p className="text-xs text-muted-foreground">Drop tasks here</p>
          </div>
        </div>
      </div>
    </div>
  );
}
