import { useState } from 'react';
import { useSortable } from '@dnd-kit/sortable';
import { CSS } from '@dnd-kit/utilities';
import { useTaskStore } from '../lib/store';
import { Badge } from './ui/badge';
import { Button } from './ui/button';
import VectalTaskModal from './VectalTaskModal';
import { MoreHorizontal, Calendar, Flag, Tag, Trash2, Edit3 } from 'lucide-react';
import { cn } from '../lib/utils';
export default function VectalTaskCard({ task, isDragging = false }) {
  const { deleteTask } = useTaskStore();
  const [showEditModal, setShowEditModal] = useState(false);
  const [showMenu, setShowMenu] = useState(false);
  const [loading, setLoading] = useState(false);
  const {
    attributes,
    listeners,
    setNodeRef,
    transform,
    transition,
    isDragging: sortableIsDragging,
  } = useSortable({ id: task.id });
  const style = {
    transform: CSS.Transform.toString(transform),
    transition,
  };
  const handleDelete = async () => {
    if (!confirm('Are you sure you want to delete this task?')) return;
    setLoading(true);
    try {
      await deleteTask(task.id);
    } catch (error) {
      console.error('Failed to delete task:', error);
    } finally {
      setLoading(false);
      setShowMenu(false);
    }
  };
  const getPriorityColor = (priority) => {
    switch (priority) {
      case 'high':
        return 'text-destructive';
      case 'medium':
        return 'text-warning';
      case 'low':
        return 'text-success';
      default:
        return 'text-muted-foreground';
    }
  };
  const getPriorityBadgeVariant = (priority) => {
    switch (priority) {
      case 'high':
        return 'destructive';
      case 'medium':
        return 'warning';
      case 'low':
        return 'success';
      default:
        return 'secondary';
    }
  };
  const formatDate = (dateString) => {
    if (!dateString) return null;
    const date = new Date(dateString);
    const now = new Date();
    const diffTime = date.getTime() - now.getTime();
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    if (diffDays === 0) return 'Today';
    if (diffDays === 1) return 'Tomorrow';
    if (diffDays === -1) return 'Yesterday';
    if (diffDays > 0) return `In ${diffDays} days`;
    return `${Math.abs(diffDays)} days ago`;
  };
  const isOverdue = (dateString) => {
    if (!dateString) return false;
    return new Date(dateString) < new Date();
  };
  if (isDragging || sortableIsDragging) {
    return (
      <div
        ref={setNodeRef}
        style={style}
        className="vectal-task-card opacity-50 rotate-3 scale-105"
      >
        <div className="flex items-start justify-between mb-2">
          <h4 className="font-medium text-foreground text-sm line-clamp-2">
            {task.title}
          </h4>
        </div>
      </div>
    );
  }
  return (
    <>
      <div
        ref={setNodeRef}
        style={style}
        {...attributes}
        {...listeners}
        className={cn(
          "vectal-task-card relative group",
          loading && "opacity-50 pointer-events-none"
        )}
      >
        <div className="flex items-start justify-between mb-3">
          <h4 className="font-medium text-foreground text-sm line-clamp-2 flex-1 pr-2">
            {task.title}
          </h4>
          <div className="relative">
            <Button
              variant="ghost"
              size="icon"
              className="h-6 w-6 opacity-0 group-hover:opacity-100 transition-opacity"
              onClick={(e) => {
                e.stopPropagation();
                setShowMenu(!showMenu);
              }}
            >
              <MoreHorizontal className="h-3 w-3" />
            </Button>
            {showMenu && (
              <div className="absolute right-0 top-7 bg-popover border border-border rounded-lg shadow-lg py-1 z-10 min-w-[120px]">
                <button
                  onClick={(e) => {
                    e.stopPropagation();
                    setShowEditModal(true);
                    setShowMenu(false);
                  }}
                  className="flex items-center gap-2 w-full px-3 py-1.5 text-sm text-foreground hover:bg-muted transition-colors"
                >
                  <Edit3 className="h-3 w-3" />
                  Edit
                </button>
                <button
                  onClick={(e) => {
                    e.stopPropagation();
                    handleDelete();
                  }}
                  className="flex items-center gap-2 w-full px-3 py-1.5 text-sm text-destructive hover:bg-destructive/10 transition-colors"
                >
                  <Trash2 className="h-3 w-3" />
                  Delete
                </button>
              </div>
            )}
          </div>
        </div>
        {task.description && (
          <p className="text-xs text-muted-foreground mb-3 line-clamp-2">
            {task.description}
          </p>
        )}
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            <Badge variant={getPriorityBadgeVariant(task.priority)} className="text-xs">
              <Flag className={cn("h-2.5 w-2.5 mr-1", getPriorityColor(task.priority))} />
              {task.priority}
            </Badge>
            {task.dueDate && (
              <Badge 
                variant={isOverdue(task.dueDate) ? "destructive" : "outline"} 
                className="text-xs"
              >
                <Calendar className="h-2.5 w-2.5 mr-1" />
                {formatDate(task.dueDate)}
              </Badge>
            )}
          </div>
        </div>
        {task.tags && task.tags.length > 0 && (
          <div className="flex flex-wrap gap-1 mt-2">
            {task.tags.slice(0, 2).map((tag, index) => (
              <Badge key={index} variant="secondary" className="text-xs">
                <Tag className="h-2 w-2 mr-1" />
                {tag}
              </Badge>
            ))}
            {task.tags.length > 2 && (
              <Badge variant="outline" className="text-xs">
                +{task.tags.length - 2}
              </Badge>
            )}
          </div>
        )}
      </div>
      {showEditModal && (
        <VectalTaskModal
          isOpen={showEditModal}
          onClose={() => setShowEditModal(false)}
          task={task}
        />
      )}
    </>
  );
}
